package com.quantchi.knowledge.center.service;

import com.quantchi.knowledge.center.bean.vo.CompanyChainVO;
import com.quantchi.knowledge.center.bean.model.NodeData;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.List;

/**
 * 企业服务测试类
 *
 * <AUTHOR>
 * @since 2025-01-16
 */
@SpringBootTest
public class CompanyServiceTest {

    @Autowired
    private ICompanyService companyService;

    @Test
    public void testGetCompanyChains() {
        // 使用一个测试企业ID
        final String companyId = "test_company_id";
        
        final List<CompanyChainVO> chains = companyService.getCompanyChains(companyId);
        
        System.out.println("企业链信息数量: " + chains.size());
        for (final CompanyChainVO chain : chains) {
            System.out.println("链ID: " + chain.getChainId() + ", 链名称: " + chain.getChainName());
        }
    }

    @Test
    public void testGetChainTreeData() {
        // 使用测试数据
        final String companyId = "test_company_id";
        final String chainId = "industry_ai";
        
        final NodeData treeData = companyService.getChainTreeData(companyId, chainId);
        
        if (treeData != null) {
            System.out.println("根节点: " + treeData.getName());
            System.out.println("是否高亮: " + treeData.getHighlighted());
            printNodeTree(treeData, 0);
        } else {
            System.out.println("未获取到树结构数据");
        }
    }

    private void printNodeTree(final NodeData node, final int level) {
        final String indent = "  ".repeat(level);
        System.out.println(indent + "- " + node.getName() + 
                          " (ID: " + node.getId() + 
                          ", 高亮: " + node.getHighlighted() + ")");
        
        if (node.getChildren() != null) {
            for (final NodeData child : node.getChildren()) {
                printNodeTree(child, level + 1);
            }
        }
    }
}
