package com.quantchi.knowledge.center.service;

import com.quantchi.knowledge.center.bean.model.IndustryRankListQuery;
import com.quantchi.knowledge.center.bean.vo.IndustryRankListVO;
import com.quantchi.knowledge.center.service.impl.IndustryOverviewServiceImpl;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 产业榜单序号功能测试
 *
 * <AUTHOR>
 * @since 2025-01-16
 */
@SpringBootTest
public class IndustryRankListRankNumberTest {

    @Autowired
    private IndustryOverviewServiceImpl industryOverviewService;

    @Test
    public void testRdStrengthRankNumbers() {
        // 测试研发实力榜序号
        final IndustryRankListQuery query = new IndustryRankListQuery();
        query.setChainId("industry_icd");
        query.setType(1); // 研发实力榜
        query.setPageNum(1);
        query.setPageSize(10);

        final IndustryRankListVO result = industryOverviewService.industryRankList(query);

        assertNotNull(result);
        assertNotNull(result.getRdStrength());

        // 验证第一页序号从1开始
        if (!result.getRdStrength().isEmpty()) {
            assertEquals(Integer.valueOf(1), result.getRdStrength().get(0).getRank());
            if (result.getRdStrength().size() > 1) {
                assertEquals(Integer.valueOf(2), result.getRdStrength().get(1).getRank());
            }
        }

        System.out.println("研发实力榜第一页序号验证通过");
    }

    @Test
    public void testRdStrengthRankNumbersSecondPage() {
        // 测试研发实力榜第二页序号
        final IndustryRankListQuery query = new IndustryRankListQuery();
        query.setChainId("industry_icd");
        query.setType(1); // 研发实力榜
        query.setPageNum(2);
        query.setPageSize(10);

        final IndustryRankListVO result = industryOverviewService.industryRankList(query);

        assertNotNull(result);
        assertNotNull(result.getRdStrength());

        // 验证第二页序号从11开始
        if (!result.getRdStrength().isEmpty()) {
            assertEquals(Integer.valueOf(11), result.getRdStrength().get(0).getRank());
            if (result.getRdStrength().size() > 1) {
                assertEquals(Integer.valueOf(12), result.getRdStrength().get(1).getRank());
            }
        }

        System.out.println("研发实力榜第二页序号验证通过");
    }

    @Test
    public void testInvestPotentialRankNumbers() {
        // 测试投资潜力榜序号
        final IndustryRankListQuery query = new IndustryRankListQuery();
        query.setChainId("industry_icd");
        query.setType(2); // 投资潜力榜
        query.setPageNum(1);
        query.setPageSize(10);

        final IndustryRankListVO result = industryOverviewService.industryRankList(query);

        assertNotNull(result);
        assertNotNull(result.getInvestPotentialList());

        // 验证序号
        if (!result.getInvestPotentialList().isEmpty()) {
            assertEquals(Integer.valueOf(1), result.getInvestPotentialList().get(0).getRank());
        }

        System.out.println("投资潜力榜序号验证通过");
    }

    @Test
    public void testCapitalPreferenceRankNumbers() {
        // 测试资本青睐榜序号
        final IndustryRankListQuery query = new IndustryRankListQuery();
        query.setChainId("industry_icd");
        query.setType(3); // 资本青睐榜
        query.setPageNum(1);
        query.setPageSize(10);

        final IndustryRankListVO result = industryOverviewService.industryRankList(query);

        assertNotNull(result);
        assertNotNull(result.getCapitalPreferenceList());

        // 验证序号
        if (!result.getCapitalPreferenceList().isEmpty()) {
            assertEquals(Integer.valueOf(1), result.getCapitalPreferenceList().get(0).getRank());
        }

        System.out.println("资本青睐榜序号验证通过");
    }

    @Test
    public void testOutwardInvestRankNumbers() {
        // 测试对外投资榜序号
        final IndustryRankListQuery query = new IndustryRankListQuery();
        query.setChainId("industry_icd");
        query.setType(4); // 对外投资榜
        query.setPageNum(1);
        query.setPageSize(10);

        final IndustryRankListVO result = industryOverviewService.industryRankList(query);

        assertNotNull(result);
        assertNotNull(result.getOutwardInvestList());

        // 验证序号
        if (!result.getOutwardInvestList().isEmpty()) {
            assertEquals(Integer.valueOf(1), result.getOutwardInvestList().get(0).getRank());
        }

        System.out.println("对外投资榜序号验证通过");
    }

    @Test
    public void testRevenueScaleRankNumbers() {
        // 测试营收规模榜序号
        final IndustryRankListQuery query = new IndustryRankListQuery();
        query.setChainId("industry_icd");
        query.setType(5); // 营收规模榜
        query.setPageNum(1);
        query.setPageSize(10);

        final IndustryRankListVO result = industryOverviewService.industryRankList(query);

        assertNotNull(result);
        assertNotNull(result.getRevenueScaleList());

        // 验证序号
        if (!result.getRevenueScaleList().isEmpty()) {
            assertEquals(Integer.valueOf(1), result.getRevenueScaleList().get(0).getRank());
        }

        System.out.println("营收规模榜序号验证通过");
    }

    @Test
    public void testRevenueProfitRankNumbers() {
        // 测试营收利润榜序号
        final IndustryRankListQuery query = new IndustryRankListQuery();
        query.setChainId("industry_icd");
        query.setType(6); // 营收利润榜
        query.setPageNum(1);
        query.setPageSize(10);

        final IndustryRankListVO result = industryOverviewService.industryRankList(query);

        assertNotNull(result);
        assertNotNull(result.getRevenueProfitList());

        // 验证序号
        if (!result.getRevenueProfitList().isEmpty()) {
            assertEquals(Integer.valueOf(1), result.getRevenueProfitList().get(0).getRank());
        }

        System.out.println("营收利润榜序号验证通过");
    }

    @Test
    public void testAssetsTotalRankNumbers() {
        // 测试链主企业榜序号
        final IndustryRankListQuery query = new IndustryRankListQuery();
        query.setChainId("industry_icd");
        query.setType(7); // 链主企业榜
        query.setPageNum(1);
        query.setPageSize(10);

        final IndustryRankListVO result = industryOverviewService.industryRankList(query);

        assertNotNull(result);
        assertNotNull(result.getAssetsTotalList());

        // 验证序号
        if (!result.getAssetsTotalList().isEmpty()) {
            assertEquals(Integer.valueOf(1), result.getAssetsTotalList().get(0).getRank());
        }

        System.out.println("链主企业榜序号验证通过");
    }
}
