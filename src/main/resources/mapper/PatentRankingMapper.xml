<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.quantchi.knowledge.center.dao.mysql.PatentRankingMapper">    
    <!-- 根据节点ID列表和地区名称获取专利排名（包含总量和授权数量） -->
    <select id="getCompanyPatentRankingWithAccreditByNode" resultType="com.quantchi.knowledge.center.bean.bo.IndustryRankRdStrengthBO">
        SELECT
            pa.applicant_id AS cid,
            pa.applicant_province AS province,
            pa.applicant_city AS city,
            COUNT(DISTINCT pn.patent_id) AS totalCount,
            SUM(CASE WHEN pa.patent_type = '发明授权' THEN 1 ELSE 0 END) AS accreditCount
        FROM
            patent_node pn
        JOIN
            patent_applicant pa ON pn.patent_id = pa.patent_id
        WHERE
            pn.is_valid = 1
        <if test="nodeIdList != null and nodeIdList.size() != 0">
            AND pn.node_id IN
            <foreach collection="nodeIdList" item="nodeId" open="(" separator="," close=")">
                #{nodeId}
            </foreach>
        </if>
        <if test="province != null and province != ''">
            AND pa.applicant_province = #{province}
        </if>
        <if test="city != null and city != ''">
            AND pa.applicant_city = #{city}
        </if>
        <choose>
            <when test="applicantType != null and applicantType == 'university'">
                AND (pa.applicant_name LIKE '%-e' OR pa.applicant_name LIKE '%大学' OR pa.applicant_name LIKE '%学院' OR pa.applicant_name LIKE '%university%')
            </when>
            <otherwise>
                AND pa.applicant_name NOT LIKE '%-e' 
                AND pa.applicant_name NOT LIKE '%大学' 
                AND pa.applicant_name NOT LIKE '%学院' 
                AND pa.applicant_name NOT LIKE '%university%'
            </otherwise>
        </choose>
        GROUP BY
            pa.applicant_id
        ORDER BY
            totalCount DESC
    </select>


</mapper>
