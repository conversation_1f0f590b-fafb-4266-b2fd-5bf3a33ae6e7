<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.quantchi.knowledge.center.dao.mysql.SysReportMapper">
    <select id="getReportList" resultType="com.quantchi.knowledge.center.bean.entity.SysReport">
        SELECT *
        FROM sys_report
        WHERE is_valid = 1
        AND display_status = 1
        <if test="keyword != null and keyword != ''">
            AND (
                title LIKE CONCAT('%', #{keyword}, '%')
                OR introduce LIKE CONCAT('%', #{keyword}, '%')
                OR summarize LIKE CONCAT('%', #{keyword}, '%')
                OR id IN (
                    SELECT report_id
                    FROM sys_report_tag_relation
                    WHERE tag_name LIKE CONCAT('%', #{keyword}, '%')
                )
            )
        </if>
        AND id IN (
        SELECT report_id
        FROM sys_report_tag_relation
        WHERE
        (
            (<if test="xinxingTagList != null and xinxingTagList.size() > 0">
            tag_name IN
            <foreach collection="xinxingTagList" item="tag" separator="," open="(" close=")">
                #{tag.name}
            </foreach>
             and
        </if> tag_type = '战新产业')
        OR
        (<if test="weilaiTagList != null and weilaiTagList.size() > 0">
        tag_name IN
        <foreach collection="weilaiTagList" item="tag" separator="," open="(" close=")">
            #{tag.name}
        </foreach>
         and
        </if>  tag_type = '未来产业')))
        ORDER BY publish_date DESC
    </select>

</mapper>