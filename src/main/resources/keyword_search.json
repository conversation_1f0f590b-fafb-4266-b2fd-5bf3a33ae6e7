{"keywordSearchList": [{"field": "icir_company", "fieldName": "企业", "requiredList": ["id", "name", "used_name", "tags", "establish_date", "province", "city", "area", "nec", "sec", "fec", "icd", "financing_round_cal", "technical_score", "business_score", "status", "legal_person", "regist_capi_value_cal", "regist_capi"], "keywordSearchList": [{"field": "name", "fieldName": "企业名", "boost": 7, "isHighlight": true}, {"field": "used_name", "fieldName": "曾用名", "boost": 6, "isHighlight": true}, {"field": "legal_person", "fieldName": "法人", "boost": 3, "isHighlight": true}]}, {"field": "icir_patent", "fieldName": "专利", "requiredList": ["id", "public_code", "apply_code", "title", "patent_type", "status", "applicants", "patentees", "inventors", "apply_date", "public_date", "abstract", "abstract_cn", "abstract_en", "score", "sec", "value", "main_ipc"], "keywordSearchList": [{"field": "title", "fieldName": "专利名称", "boost": 7, "isHighlight": true}, {"field": "applicants.name", "fieldName": "申请（专利权）人", "type": "keyword", "boost": 7, "isHighlight": true}, {"field": "inventors.name", "fieldName": "发明人", "type": "keyword", "boost": 7, "isHighlight": true}, {"field": "apply_code", "fieldName": "申请号", "type": "keyword", "boost": 3, "isHighlight": true}, {"field": "public_code", "fieldName": "公开号", "type": "keyword", "boost": 3, "isHighlight": true}]}, {"field": "icir_news", "fieldName": "资讯", "requiredList": ["id", "title", "publish_date", "source", "industry", "sentiment", "entities", "url", "tags", "abstract", "sec", "fec"], "keywordSearchList": [{"field": "title", "boost": 10, "fieldName": "标题", "isHighlight": true}]}, {"field": "icir_financing", "fieldName": "投融资", "requiredList": ["id", "cid", "financing_company", "company_tags", "industry", "financing_date", "financing_round", "investors", "financing_value_cal", "financing_unit", "province", "city", "investors"], "keywordSearchList": [{"field": "financing_company", "fieldName": "融资企业名称", "boost": 7, "isHighlight": true}, {"field": "investors", "fieldName": "投资方名称", "boost": 3, "isHighlight": true}]}, {"field": "icir_node", "fieldName": "产业", "requiredList": ["chain_id", "chain_name", "node_id", "node_level", "node_name"], "keywordSearchList": [{"field": "node_name", "fieldName": "名称", "boost": 10, "isHighlight": true}, {"field": "chain_name", "fieldName": "链名称", "boost": 3, "isHighlight": true}]}, {"field": "icir_ipc", "fieldName": "技术", "requiredList": ["id", "code", "technology", "desc", "name"], "keywordSearchList": [{"field": "technology", "fieldName": "名称", "boost": 10, "isHighlight": true}, {"field": "code", "fieldName": "分类号", "boost": 5, "isHighlight": true}]}, {"field": "icir_report", "fieldName": "研报", "requiredList": ["id", "title", "publish_date", "industry", "source", "institution", "authors", "type", "url", "sec", "fec"], "keywordSearchList": [{"field": "title", "fieldName": "标题", "boost": 10, "isHighlight": true}]}, {"field": "icir_policy", "fieldName": "政策", "requiredList": ["id", "title", "publish_date", "industry", "source", "type", "url", "level", "city", "content", "sec", "fec"], "keywordSearchList": [{"field": "title", "fieldName": "标题", "boost": 10, "isHighlight": true}]}]}