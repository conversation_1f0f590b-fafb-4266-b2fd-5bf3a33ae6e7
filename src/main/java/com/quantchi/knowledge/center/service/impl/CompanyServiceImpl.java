package com.quantchi.knowledge.center.service.impl;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.text.CharSequenceUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.quantchi.knowledge.center.bean.constant.Constants;
import com.quantchi.knowledge.center.bean.entity.*;
import com.quantchi.knowledge.center.bean.enums.EsIndexEnum;
import com.quantchi.knowledge.center.bean.exception.BusinessException;
import com.quantchi.knowledge.center.bean.vo.CompanyMenuItemVO;
import com.quantchi.knowledge.center.bean.vo.CompanyVO;
import com.quantchi.knowledge.center.bean.vo.NameCountVO;
import com.quantchi.knowledge.center.bean.vo.PatentInfoDetailVO;
import com.quantchi.knowledge.center.dao.mysql.CompanyInvestMapper;
import com.quantchi.knowledge.center.dao.mysql.CompanyMapper;
import com.quantchi.knowledge.center.dao.mysql.SysUserFollowMapper;
import com.quantchi.knowledge.center.helper.ElasticsearchHelper;
import com.quantchi.knowledge.center.service.*;
import com.quantchi.knowledge.center.util.DateHandlerUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.io.IOException;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-08
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class CompanyServiceImpl extends ServiceImpl<CompanyMapper, Company> implements ICompanyService {

    private final ICompanyTagService companyTagService;

    private final ElasticsearchHelper elasticsearchHelper;

    private final ICompanyStockService companyStockService;

    private final SchemaSecService schemaSecService;

    private final SchemaFecService schemaFecService;

    private final SchemaIcdService schemaIcdService;

    private final CompanyInvestMapper companyInvestMapper;

    private final SysUserFollowMapper sysUserFollowMapper;

    private final IDmDivisionService dmDivisionService;

    private final ICompanyBusinessDimensionService companyBusinessDimensionService;

    private final ICompanyTechnicalDimensionService companyTechnicalDimensionService;

    private final ICompanyFinancingService companyFinancingService;

    private final ICompanyPunishService companyPunishService;

    private final ICompanyAbnormalService companyAbnormalService;

    private final ICompanyIllegalService companyIllegalService;

    private final ICompanyJudgementService companyJudgementService;

    private final ThreadPoolTaskExecutor docExecutor;

    /**
     * 获取企业画像菜单项
     *
     * @param id 企业ID
     * @return 菜单项列表
     */
    @Override
    public List<CompanyMenuItemVO> getCompanyMenuItems(final String id) {
        try {
            // 并行获取各个菜单项的显示状态
            // 企业总览菜单相关的显示状态
            CompletableFuture<Boolean> overviewSummaryFuture = CompletableFuture.supplyAsync(() ->
                    shouldDisplayOverviewSummary(id), docExecutor);
            CompletableFuture<Boolean> overviewBasicFuture = CompletableFuture.supplyAsync(() ->
                    shouldDisplayOverviewBasic(id), docExecutor);

            // 企业画像菜单相关的显示状态
            CompletableFuture<Boolean> baseInfoCommerceFuture = CompletableFuture.supplyAsync(() ->
                    shouldDisplayBaseInfoCommerce(id), docExecutor);
            CompletableFuture<Boolean> baseInfoTechnologyFuture = CompletableFuture.supplyAsync(() ->
                    shouldDisplayBaseInfoTechnology(id), docExecutor);
            CompletableFuture<Boolean> baseInfoCapitalFuture = CompletableFuture.supplyAsync(() ->
                    shouldDisplayBaseInfoCapital(id), docExecutor);
            CompletableFuture<Boolean> baseInfoRiskFuture = CompletableFuture.supplyAsync(() ->
                    shouldDisplayBaseInfoRisk(id), docExecutor);

            // 研究工具菜单相关的显示状态
            CompletableFuture<Boolean> toolsCompanyFuture = CompletableFuture.supplyAsync(() ->
                    shouldDisplayToolsCompany(id), docExecutor);

            // 等待所有并行任务完成
            CompletableFuture<Void> allFutures = CompletableFuture.allOf(
                    overviewSummaryFuture, overviewBasicFuture,
                    baseInfoCommerceFuture, baseInfoTechnologyFuture, baseInfoCapitalFuture, baseInfoRiskFuture, toolsCompanyFuture
            );

            // 等待所有任务完成
            allFutures.join();

            // 创建企业总览菜单的子菜单项
            List<CompanyMenuItemVO> overviewChildren = Arrays.asList(
                    CompanyMenuItemVO.builder()
                            .label("企业概述")
                            .key("overview-summary")
                            .display(overviewSummaryFuture.get())
                            .build(),
                    CompanyMenuItemVO.builder()
                            .label("基本信息")
                            .key("overview-basic")
                            .display(overviewBasicFuture.get())
                            .build()
            );

            // 创建企业画像菜单的子菜单项
            List<CompanyMenuItemVO> baseInfoChildren = Arrays.asList(
                    CompanyMenuItemVO.builder()
                            .label("经营分析")
                            .key("baseInfo-commerce")
                            .display(baseInfoCommerceFuture.get())
                            .build(),
                    CompanyMenuItemVO.builder()
                            .label("技术洞察")
                            .key("baseInfo-technology")
                            .display(baseInfoTechnologyFuture.get())
                            .build(),
                    CompanyMenuItemVO.builder()
                            .label("资金穿透")
                            .key("baseInfo-capital")
                            .display(baseInfoCapitalFuture.get())
                            .build(),
                    CompanyMenuItemVO.builder()
                            .label("风险预警")
                            .key("baseInfo-risk")
                            .display(baseInfoRiskFuture.get())
                            .build()
            );

            // 创建研究工具菜单的子菜单项
            List<CompanyMenuItemVO> toolsChildren = Arrays.asList(
                    CompanyMenuItemVO.builder()
                            .label("企业数据")
                            .key("tools-company")
                            .display(toolsCompanyFuture.get())
                            .build()
            );

            // 根据子菜单项的显示状态来决定父菜单的显示状态
            // 如果子菜单中有一个显示为true，则父菜单显示为true
            boolean overviewMenuDisplayFromChildren = overviewChildren.stream().anyMatch(CompanyMenuItemVO::getDisplay);
            boolean baseInfoMenuDisplayFromChildren = baseInfoChildren.stream().anyMatch(CompanyMenuItemVO::getDisplay);
            boolean toolsMenuDisplayFromChildren = toolsChildren.stream().anyMatch(CompanyMenuItemVO::getDisplay);

            // 创建企业总览菜单
            CompanyMenuItemVO overviewMenu = CompanyMenuItemVO.builder()
                    .label("企业总览")
                    .key("overview")
                    .display(overviewMenuDisplayFromChildren) // 使用子菜单决定的显示状态
                    .children(overviewChildren)
                    .build();

            // 创建企业画像菜单
            CompanyMenuItemVO baseInfoMenu = CompanyMenuItemVO.builder()
                    .label("企业画像")
                    .key("baseInfo")
                    .display(baseInfoMenuDisplayFromChildren) // 使用子菜单决定的显示状态
                    .children(baseInfoChildren)
                    .build();

            // 创建研究工具菜单
            CompanyMenuItemVO toolsMenu = CompanyMenuItemVO.builder()
                    .label("研究工具")
                    .key("tools")
                    .display(toolsMenuDisplayFromChildren) // 使用子菜单决定的显示状态
                    .children(toolsChildren)
                    .build();

            // 返回所有菜单项
            return Arrays.asList(overviewMenu, baseInfoMenu, toolsMenu);
        } catch (Exception e) {
            log.error("并行获取菜单项显示状态失败", e);
            // 发生异常时返回默认菜单项
            return getDefaultMenuItems(id);
        }
    }

    /**
     * 获取默认的菜单项（所有菜单项都显示）
     *
     * @param id 企业ID
     * @return 默认菜单项列表
     */
    private List<CompanyMenuItemVO> getDefaultMenuItems(final String id) {
        // 创建子菜单项
        List<CompanyMenuItemVO> overviewChildren = Arrays.asList(
                CompanyMenuItemVO.builder()
                        .label("企业概述")
                        .key("overview-summary")
                        .display(true)
                        .build(),
                CompanyMenuItemVO.builder()
                        .label("基本信息")
                        .key("overview-basic")
                        .display(true)
                        .build()
        );

        List<CompanyMenuItemVO> baseInfoChildren = Arrays.asList(
                CompanyMenuItemVO.builder()
                        .label("经营分析")
                        .key("baseInfo-commerce")
                        .display(true)
                        .build(),
                CompanyMenuItemVO.builder()
                        .label("技术洞察")
                        .key("baseInfo-technology")
                        .display(true)
                        .build(),
                CompanyMenuItemVO.builder()
                        .label("资金穿透")
                        .key("baseInfo-capital")
                        .display(true)
                        .build(),
                CompanyMenuItemVO.builder()
                        .label("风险预警")
                        .key("baseInfo-risk")
                        .display(true)
                        .build()
        );

        List<CompanyMenuItemVO> toolsChildren = Arrays.asList(
                CompanyMenuItemVO.builder()
                        .label("企业数据")
                        .key("tools-company")
                        .display(true)
                        .build()
        );

        // 根据子菜单决定父菜单的显示状态
        boolean overviewMenuDisplay = overviewChildren.stream().anyMatch(CompanyMenuItemVO::getDisplay);
        boolean baseInfoMenuDisplay = baseInfoChildren.stream().anyMatch(CompanyMenuItemVO::getDisplay);
        boolean toolsMenuDisplay = toolsChildren.stream().anyMatch(CompanyMenuItemVO::getDisplay);

        // 创建企业总览菜单
        CompanyMenuItemVO overviewMenu = CompanyMenuItemVO.builder()
                .label("企业总览")
                .key("overview")
                .display(overviewMenuDisplay) // 根据子菜单决定显示状态
                .children(overviewChildren)
                .build();

        // 创建企业画像菜单
        CompanyMenuItemVO baseInfoMenu = CompanyMenuItemVO.builder()
                .label("企业画像")
                .key("baseInfo")
                .display(baseInfoMenuDisplay) // 根据子菜单决定显示状态
                .children(baseInfoChildren)
                .build();

        // 创建研究工具菜单
        CompanyMenuItemVO toolsMenu = CompanyMenuItemVO.builder()
                .label("研究工具")
                .key("tools")
                .display(toolsMenuDisplay) // 根据子菜单决定显示状态
                .children(toolsChildren)
                .build();

        // 返回所有菜单项
        return Arrays.asList(overviewMenu, baseInfoMenu, toolsMenu);
    }

    /**
     * 判断是否显示企业概述菜单项
     *
     * @param id 企业ID
     * @return 是否显示
     */
    private boolean shouldDisplayOverviewSummary(final String id) {
        // 可以查询数据库中是否有企业概述相关数据
        // 这里作为示例，默认返回 true
        return this.getById(id) != null;
    }

    /**
     * 判断是否显示基本信息菜单项
     *
     * @param id 企业ID
     * @return 是否显示
     */
    private boolean shouldDisplayOverviewBasic(final String id) {
        // 可以查询数据库中是否有基本信息相关数据
        // 这里作为示例，默认返回 true
        return this.getById(id) != null;
    }

    /**
     * 判断是否显示经营分析菜单项
     *
     * @param id 企业ID
     * @return 是否显示
     */
    private boolean shouldDisplayBaseInfoCommerce(final String id) {
        // 可以查询数据库中是否有经营分析相关数据
        // 例如，检查是否有 business_score 数据
        try {
            return companyBusinessDimensionService.getOne(Wrappers.<CompanyBusinessDimension>lambdaQuery()
                    .eq(CompanyBusinessDimension::getCid, id)) != null;
        } catch (Exception e) {
            log.error("查询经营分析数据失败", e);
            return false;
        }
    }

    /**
     * 判断是否显示技术洞察菜单项
     *
     * @param id 企业ID
     * @return 是否显示
     */
    private boolean shouldDisplayBaseInfoTechnology(final String id) {
        // 可以查询数据库中是否有技术洞察相关数据
        // 例如，检查是否有 technical_score 数据
        try {
            // 查询专利数量是否不为0
            return true;
//            return elasticsearchHelper.countRequest(EsIndexEnum.PATENT.getEsIndex(), genCidQuery(id)) > 0;
//            return companyTechnicalDimensionService.getOne(Wrappers.<CompanyTechnicalDimension>lambdaQuery()
//                    .eq(CompanyTechnicalDimension::getCid, id)) != null;
        } catch (Exception e) {
            log.error("查询技术洞察数据失败", e);
            return false;
        }
    }

    /**
     * 判断是否显示资金穿透菜单项
     *
     * @param id 企业ID
     * @return 是否显示
     */
    private boolean shouldDisplayBaseInfoCapital(final String id) {
        // 可以查询数据库中是否有资金穿透相关数据
        // 例如，检查是否有股东或投资信息
        try {
            return CollUtil.isNotEmpty(companyFinancingService.list(Wrappers.<CompanyFinancing>lambdaQuery()
                    .eq(CompanyFinancing::getCid, id)));
        } catch (Exception e) {
            log.error("查询资金穿透数据失败", e);
            return false;
        }
    }

    /**
     * 判断是否显示风险预警菜单项
     *
     * @param id 企业ID
     * @return 是否显示
     */
    private boolean shouldDisplayBaseInfoRisk(final String id) {
        // 可以查询数据库中是否有风险预警相关数据
        // 例如，检查是否有风险相关的数据
        try {
            // 需求变更，数据为0的时候也显示，只不过列表不展示，那么这个总模块始终显示
            return true;
//            return companyPunishService.count(Wrappers.<CompanyPunish>lambdaQuery()
//                    .eq(CompanyPunish::getCid, id)) > 0 ||
//                    companyAbnormalService.count(Wrappers.<CompanyAbnormal>lambdaQuery()
//                            .eq(CompanyAbnormal::getCid, id)) > 0 ||
//                    companyIllegalService.count(Wrappers.<CompanyIllegal>lambdaQuery()
//                            .eq(CompanyIllegal::getCid, id).isNull(CompanyIllegal::getOrgOut)) > 0 ||
//                    companyJudgementService.count(Wrappers.<CompanyJudgement>lambdaQuery()
//                            .eq(CompanyJudgement::getDefendantCid, id)) > 0;
        } catch (Exception e) {
            log.error("查询风险预警数据失败", e);
            return false;
        }
    }

    /**
     * 判断是否显示企业数据菜单项
     *
     * @param id 企业ID
     * @return 是否显示
     */
    private boolean shouldDisplayToolsCompany(final String id) {
        // 可以查询数据库中是否有企业数据相关数据
        // 这里作为示例，默认返回 true
        try {
            final PatentInfoDetailVO patentInfoDetail = companyTechnicalDimensionService.getPatentInfoDetail(id, null, null, 1, 1);
            return patentInfoDetail.getTotal() > 0L;
        } catch (IOException e) {
            return false;
        }
    }


    @Override
    public CompanyVO baseInfo(final String id) {
        final CompanyVO vo = new CompanyVO();
        final Company company = this.getById(id);
        if (company == null) {
            throw new BusinessException("企业不存在");
        }
        final Map<String, Object> source = elasticsearchHelper.getDataById(EsIndexEnum.COMPANY.getEsIndex(), id, new String[]{"id", "sec", "nec", "fec", "icd"}, new String[]{});
        final Long userId = StpUtil.getLoginIdAsLong();
        final List<CompletableFuture<Void>> futureList = new ArrayList<>(7);
        futureList.add(CompletableFuture.runAsync(() -> {
            // 获取企业标签
            final List<CompanyTag> companyTagList = companyTagService.list(Wrappers.<CompanyTag>lambdaQuery()
                    .eq(CompanyTag::getCid, id));
            if (CollUtil.isNotEmpty(companyTagList)) {
                final Set<String> tagSet = companyTagList.stream().map(CompanyTag::getTagName).collect(Collectors.toSet());
                vo.setTagList(tagSet);
                vo.setIsListed(!CollUtil.intersection(tagSet, Constants.COMPANY_LISTED_TAG).isEmpty());
            }
        }, docExecutor));
        futureList.add(CompletableFuture.runAsync(() -> {
            vo.setId(company.getId());
            vo.setName(company.getName());
            vo.setUsedName(company.getUsedName());
            final String logo = company.getLogo();
            if (CharSequenceUtil.isNotBlank(logo)) {
                vo.setLogo("https:" + CharSequenceUtil.removePrefix((String) logo, "http:"));
            }
            vo.setStatus(company.getStatus());
            vo.setCreditCode(company.getCreditCode());
            vo.setLegalPerson(company.getLegalPerson());
            vo.setEstablishDate(company.getEstablishDate());
            vo.setRegistCapi(company.getRegistCapi());
            vo.setCompanyScale(company.getCompanyScale());
            vo.setDescription(company.getDescription());
            // todo 拆分电话和邮箱
            vo.setTelList(Collections.singletonList(company.getTel()));
            vo.setEmailList(Collections.singletonList(company.getEmail()));
            vo.setWebsite(company.getWebsite());
            vo.setAddress(company.getAddress());
            vo.setPayCapi(company.getPayCapi());
            vo.setCompanyType(company.getCompanyType());
            vo.setRegistrationId(company.getRegistrationId());
            vo.setEmployeeSize(company.getEmployeeSize());
            vo.setInsuredNumber(company.getInsuredNumber());
            vo.setBusinessTerm(company.getBusinessTerm());
            vo.setBelongOrg(company.getBelongOrg());
            vo.setCheckDate(company.getCheckDate());
            vo.setBelongArea(Constants.getBelongArea(company.getProvince(), company.getCity(), company.getArea()));
            vo.setBusinessScope(company.getBusinessScope());
            vo.setUpdateTime(company.getUpdateTime());
            // 国民经济行业分类
            final ArrayList<String> nationIndustryList = new ArrayList<>(Arrays.asList(company.getNationIndustry1(), company.getNationIndustry2(), company.getNationIndustry3(), company.getNationIndustry4()));
            nationIndustryList.removeIf(CharSequenceUtil::isBlank);
            vo.setNationIndustryList(nationIndustryList);
        }, docExecutor));
        futureList.add(CompletableFuture.runAsync(() -> {
            //  新兴产业分类 从es的企业表的sec字段获取
            final Object sec = source.get("sec");
            if (sec instanceof List && CollUtil.isNotEmpty((List) sec)) {
                final List<String> secList = (List<String>) sec;
                vo.setStrategyNewIndustryList(schemaSecService.getIndustryList(secList.get(secList.size() - 1)));
            }
        }, docExecutor));
        futureList.add(CompletableFuture.runAsync(() -> {
            // 未来产业分类
            final Object fec = source.get("fec");
            if (fec instanceof List && CollUtil.isNotEmpty((List) fec)) {
                final List<String> fecList = (List<String>) fec;
                vo.setFutureIndustryList(schemaFecService.getIndustryList(fecList.get(fecList.size() - 1)));
            }
        }, docExecutor));
        futureList.add(CompletableFuture.runAsync(() -> {
            // ICD产业分类
            final Object icd = source.get("icd");
            if (icd instanceof List && CollUtil.isNotEmpty((List) icd)) {
                final List<String> icdList = (List<String>) icd;
                vo.setIcdIndustryList(schemaIcdService.getIndustryList(icdList.get(icdList.size() - 1)));
            }
        }, docExecutor));
        futureList.add(CompletableFuture.runAsync(() -> {
            // 股票信息
            final List<CompanyStock> companyStockList = companyStockService.list(Wrappers.<CompanyStock>lambdaQuery()
                    .eq(CompanyStock::getCid, id));
            if (CollUtil.isNotEmpty(companyStockList)) {
                vo.setStockCode(String.join(",", companyStockList.stream().map(CompanyStock::getStockCode)
                        .collect(Collectors.toSet())));
                vo.setShortName(String.join(",", companyStockList.stream().map(CompanyStock::getShortName)
                        .collect(Collectors.toSet())));
            }
        }, docExecutor));
        futureList.add(CompletableFuture.runAsync(() -> {
            //根据企业id获取直接投资记录（根据被投资企业去重）
            final List<CompanyInvest> companyInvestList = companyInvestMapper.getDirectInvestByCid(id, null, null, 1);
            if (CollUtil.isNotEmpty(companyInvestList)) {
                // 投资地区分布
                final Map<String, List<CompanyInvest>> collect = companyInvestList.stream()
                        .filter(companyInvest -> CharSequenceUtil.isNotBlank(companyInvest.getInvestProvinceCode())).collect(Collectors.groupingBy(CompanyInvest::getInvestProvinceCode));
                final Set<String> provinceCodeList = collect.keySet();
                if (CollUtil.isNotEmpty(provinceCodeList)) {
                    final List<DmDivision> dmDivisionList = dmDivisionService.list(Wrappers.<DmDivision>lambdaQuery()
                            .in(DmDivision::getCode, provinceCodeList));
                    final Map<String, String> codeNameMap = dmDivisionList.stream().collect(Collectors.toMap(DmDivision::getCode, DmDivision::getShortName));
                    final List<NameCountVO> investAreaList = new ArrayList<>(collect.size());
                    for (final Map.Entry<String, List<CompanyInvest>> entry : collect.entrySet()) {
                        final String key = entry.getKey();
                        final List<CompanyInvest> value = entry.getValue();
                        investAreaList.add(new NameCountVO(codeNameMap.get(key), (long) value.size()));
                    }
                    vo.setInvestAreaList(investAreaList.stream().sorted(Comparator.comparing(NameCountVO::getCount).reversed()).collect(Collectors.toList()));
                }
                // 被投资企业成立日期分布
                final Map<Date, List<CompanyInvest>> establishDateMap = companyInvestList.stream()
                        .filter(companyInvest -> companyInvest.getInvestedEstablishedDate() != null).collect(Collectors.groupingBy(CompanyInvest::getInvestedEstablishedDate));
                final List<NameCountVO> investEstablishDateList = new ArrayList<>(establishDateMap.size());
                final Map<String, Long> dateMap = new HashMap<>(establishDateMap.size());
                for (final Map.Entry<Date, List<CompanyInvest>> entry : establishDateMap.entrySet()) {
                    final Date key = entry.getKey();
                    final List<CompanyInvest> value = entry.getValue();
                    final String year = DateUtil.format(key, "yyyy");
                    if (dateMap.containsKey(year)) {
                        dateMap.put(year, dateMap.get(year) + value.size());
                    } else {
                        dateMap.put(year, (long) value.size());
                    }
                }
                // 只获取近十年的数据
                final List<String> years = DateHandlerUtil.getLatest10Year();
                years.forEach(year -> {
                    investEstablishDateList.add(new NameCountVO(year, dateMap.getOrDefault(year, 0L)));
                });
                vo.setInvestCompanyDateList(investEstablishDateList);

                //判断是否用户关注
                final List<String> followStatusList = selectFollowStatusById("企业",
                        Collections.singletonList(vo.getId()), userId);
                if (!CollectionUtils.isEmpty(followStatusList)) {
                    if (followStatusList.contains(vo.getId())) {
                        vo.setFollowStatus(1);
                    } else {
                        vo.setFollowStatus(0);
                    }
                } else {
                    vo.setFollowStatus(0);
                }
            }
        }, docExecutor));
        final CompletableFuture<Void> allFutures =
                CompletableFuture.allOf(futureList.toArray(new CompletableFuture[7]));
        try {
            allFutures.get();
        } catch (final Exception e) {
            log.error(e.getMessage(), e);
        }
        return vo;
    }

    /**
     * 查询关注状态
     * 只返回正在关注的关联id
     *
     * @param type
     * @param infoIdList
     * @return
     */
    public List<String> selectFollowStatusById(final String type, final List<String> infoIdList, final Long userId) {
        if (CollectionUtils.isEmpty(infoIdList)) {
            return null;
        }
        return sysUserFollowMapper.selectFollowStatusById(userId, type, infoIdList);
    }
}
