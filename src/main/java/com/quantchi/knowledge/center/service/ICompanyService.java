package com.quantchi.knowledge.center.service;

import com.quantchi.knowledge.center.bean.entity.Company;
import com.baomidou.mybatisplus.extension.service.IService;
import com.quantchi.knowledge.center.bean.vo.CompanyMenuItemVO;
import com.quantchi.knowledge.center.bean.vo.CompanyVO;
import com.quantchi.knowledge.center.bean.vo.CompanyChainVO;
import com.quantchi.knowledge.center.bean.model.NodeData;

import java.util.List;

/**
 * <p>
 * 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-08
 */
public interface ICompanyService extends IService<Company> {

    CompanyVO baseInfo(String id);

    /**
     * 获取企业画像菜单项
     *
     * @param id 企业ID
     * @return 菜单项列表
     */
    List<CompanyMenuItemVO> getCompanyMenuItems(String id);

    /**
     * 获取企业对应的所有链信息
     *
     * @param companyId 企业ID
     * @return 链信息列表
     */
    List<CompanyChainVO> getCompanyChains(String companyId);

    /**
     * 根据链ID获取链图树结构数据，并高亮显示当前企业的节点
     *
     * @param companyId 企业ID
     * @param chainId 链ID
     * @return 链图树结构数据
     */
    NodeData getChainTreeData(String companyId, String chainId);
}
