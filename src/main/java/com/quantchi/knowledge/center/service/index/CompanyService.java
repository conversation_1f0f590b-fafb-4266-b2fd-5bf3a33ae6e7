package com.quantchi.knowledge.center.service.index;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.quantchi.knowledge.ai.bo.GeoQueryBO;
import com.quantchi.knowledge.center.bean.constant.Constants;
import com.quantchi.knowledge.center.bean.entity.Company;
import com.quantchi.knowledge.center.bean.entity.CompanyTagSchema;
import com.quantchi.knowledge.center.bean.enums.EsIndexEnum;
import com.quantchi.knowledge.center.bean.export.IcdLibraryCompanyListExport;
import com.quantchi.knowledge.center.bean.export.IpcLibraryCompanyListExport;
import com.quantchi.knowledge.center.bean.model.*;
import com.quantchi.knowledge.center.config.properties.KeywordSearchProperties;
import com.quantchi.knowledge.center.helper.ElasticsearchHelper;
import com.quantchi.knowledge.center.service.ICompanyService;
import com.quantchi.knowledge.center.service.ICompanyTagSchemaService;
import com.quantchi.knowledge.center.service.IDmDivisionService;
import com.quantchi.knowledge.center.service.INodeService;
import com.quantchi.knowledge.center.service.impl.LibraryInfoService;
import com.quantchi.knowledge.center.util.ElasticsearchBuilder;
import lombok.extern.slf4j.Slf4j;
import org.elasticsearch.common.geo.GeoDistance;
import org.elasticsearch.common.unit.DistanceUnit;
import org.elasticsearch.index.query.*;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.elasticsearch.search.fetch.subphase.highlight.HighlightBuilder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.IOException;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Slf4j
public class CompanyService extends LibraryInfoService {

    @Autowired
    private ICompanyService companyService;

    @Autowired
    private INodeService nodeService;

    @Autowired
    private ThreadPoolTaskExecutor docExecutor;

    @Autowired
    private ICompanyTagSchemaService companyTagSchemaService;

    public CompanyService(final ElasticsearchHelper elasticsearchHelper, final NavigationSettings navigationSettings, final KeywordSearchProperties keywordSearchProperties, final IDmDivisionService dmDivisionService) {
        super(elasticsearchHelper, navigationSettings, keywordSearchProperties, dmDivisionService);
    }
    
    /**
     * 实现特定于企业索引的查询逻辑
     * 
     * @param index 索引名称
     * @param processedKeyword 关键词
     * @param boolQuery 查询条件构建器
     * @param highlightBuilder 高亮构建器
     * @param keywordSearchList 关键词搜索属性列表
     * @return 如果处理了该索引的查询逻辑返回true，否则返回false
     */
    @Override
    protected boolean applySpecificKeywordQuery(String index, 
                                             String processedKeyword,
                                             BoolQueryBuilder boolQuery,
                                             HighlightBuilder highlightBuilder,
                                             List<KeywordSearchProperty> keywordSearchList) {
        // 仅处理企业索引
        if (!EsIndexEnum.COMPANY.getEsIndex().equals(index)) {
            return false;
        }
        
        // 企业索引特定处理逻辑
        // 调整权重 - 企业名：注册资本：法人 = 7:2:1
        List<KeywordSearchProperty> adjustedKeywordSearchList = adjustCompanySearchWeights(keywordSearchList);
        
        // 构建基础查询
        final BasicBoolQuery queryBO = new BasicBoolQuery();
        queryBO.setKeyword(processedKeyword);
        queryBO.setKeywordFields(adjustedKeywordSearchList);
        queryBO.setOperator(Operator.AND);
        queryBO.setBoolQueryBuilder(boolQuery);
        queryBO.setIndex(index);
        ElasticsearchBuilder.buildBoolQuery(queryBO);
        
        // 添加企业名称搜索，大幅提高完全匹配的权重
        BoolQueryBuilder nameQueryBuilder = QueryBuilders.boolQuery();

        // 优先级1: 精确匹配 name.term
        TermQueryBuilder exactMatchTermQuery = QueryBuilders.termQuery("name.term", processedKeyword)
                .boost(20.0f);

        // 优先级2: 短语匹配 name
        MatchPhraseQueryBuilder phraseMatchQuery = QueryBuilders.matchPhraseQuery("name", processedKeyword)
                .boost(20.0f);

        // 优先级3: Ngram匹配 name.ngram
        MatchQueryBuilder ngramMatchQuery = QueryBuilders.matchQuery("name.ngram", processedKeyword)
                .boost(20.0f);

        // 优先级4: IK分词匹配 name (AND 操作)
        MatchQueryBuilder ikMatchQuery = QueryBuilders.matchQuery("name", processedKeyword)
                .minimumShouldMatch("75%")
                .boost(5.0f);

        nameQueryBuilder.should(exactMatchTermQuery);
        nameQueryBuilder.should(ikMatchQuery);
        nameQueryBuilder.should(phraseMatchQuery);
        nameQueryBuilder.should(ngramMatchQuery);
        
        // 曾用名搜索
        nameQueryBuilder.should(QueryBuilders.matchPhraseQuery("used_name", processedKeyword).boost(5.0f));
        
        // 法人搜索
        nameQueryBuilder.should(QueryBuilders.matchPhraseQuery("legal_person", processedKeyword).boost(1.0f));
        // 确保 name/used_name/legal_person 中至少有一个条件匹配
        nameQueryBuilder.minimumShouldMatch(1); 
        
        boolQuery.must(nameQueryBuilder);
        
        // 设置企业状态过滤条件，大幅提高在业和存续企业的权重，注销企业排在最后
        BoolQueryBuilder statusQueryBuilder = QueryBuilders.boolQuery();
        statusQueryBuilder.should(QueryBuilders.termQuery("status", "在业").boost(10.0f));
        statusQueryBuilder.should(QueryBuilders.termQuery("status", "存续").boost(9.0f));
        statusQueryBuilder.should(QueryBuilders.termQuery("status", "开业").boost(3.0f));
        boolQuery.should(statusQueryBuilder);
        
        // 使用must_not降低注销企业的排名
        BoolQueryBuilder cancelledStatusQueryBuilder = QueryBuilders.boolQuery();
        cancelledStatusQueryBuilder.should(QueryBuilders.termQuery("status", "注销"));
        cancelledStatusQueryBuilder.should(QueryBuilders.termQuery("status", "吁销"));
        cancelledStatusQueryBuilder.should(QueryBuilders.termQuery("status", "除名"));
        cancelledStatusQueryBuilder.should(QueryBuilders.termQuery("status", "撤销"));
        cancelledStatusQueryBuilder.should(QueryBuilders.termQuery("status", "责令关闭"));
        cancelledStatusQueryBuilder.should(QueryBuilders.termQuery("status", "已歇业"));
        
        // 使用must_not将注销企业降权
        boolQuery.mustNot(cancelledStatusQueryBuilder);
        
        // 将不同类型的条件分开处理，以便更精确地控制各个因素的权重
        
        // 1. 企业标签和规模 - 单独处理企业标签和规模
        BoolQueryBuilder tagsQueryBuilder = QueryBuilders.boolQuery();
        tagsQueryBuilder.should(QueryBuilders.existsQuery("tags").boost(180.0f));
        tagsQueryBuilder.should(QueryBuilders.termQuery("company_scale", "大型").boost(80.0f));
        // 设置最小匹配数为0，确保不满足条件时不会影响其他评分
        tagsQueryBuilder.minimumShouldMatch(1);
        boolQuery.should(tagsQueryBuilder.boost(1.5f));
        boolQuery.should(QueryBuilders.termQuery("tags.name", "国家级").boost(100.0f));
        
        // 2. 技术得分 - 单独处理技术得分，确保高分企业获得更高权重
        boolQuery.should(QueryBuilders.rangeQuery("technical_score").gte(90).boost(100.0f));
        BoolQueryBuilder technicalScoreQueryBuilder = QueryBuilders.boolQuery();
        technicalScoreQueryBuilder.should(QueryBuilders.rangeQuery("technical_score").gte(80).lt(90).boost(80.0f));
        technicalScoreQueryBuilder.should(QueryBuilders.rangeQuery("technical_score").gte(70).lt(80).boost(30.0f));
        technicalScoreQueryBuilder.should(QueryBuilders.rangeQuery("technical_score").gte(60).lt(70).boost(20.0f));
        technicalScoreQueryBuilder.should(QueryBuilders.existsQuery("technical_score").boost(15.0f));
        // 设置最小匹配数为0，确保不满足条件时不会影响其他评分
        technicalScoreQueryBuilder.minimumShouldMatch(0);
        boolQuery.should(technicalScoreQueryBuilder.boost(1.3f));
        
        // 3. 商业得分 - 单独处理商业得分
        boolQuery.should(QueryBuilders.rangeQuery("business_score").gte(90).boost(150.0f));
        BoolQueryBuilder businessScoreQueryBuilder = QueryBuilders.boolQuery();
        businessScoreQueryBuilder.should(QueryBuilders.rangeQuery("business_score").gte(80).lt(90).boost(80.0f));
        businessScoreQueryBuilder.should(QueryBuilders.rangeQuery("business_score").gte(70).lt(80).boost(30.0f));
        businessScoreQueryBuilder.should(QueryBuilders.rangeQuery("business_score").gte(60).lt(70).boost(20.0f));
        businessScoreQueryBuilder.should(QueryBuilders.existsQuery("business_score").boost(15.0f));
        // 设置最小匹配数为0，确保不满足条件时不会影响其他评分
        businessScoreQueryBuilder.minimumShouldMatch(0);
        boolQuery.should(businessScoreQueryBuilder.boost(1.2f));
        
        // 4. 网站和Logo - 单独处理网站和Logo信息
        BoolQueryBuilder webInfoQueryBuilder = QueryBuilders.boolQuery();
        webInfoQueryBuilder.should(QueryBuilders.existsQuery("website").boost(15.0f));
        webInfoQueryBuilder.should(QueryBuilders.existsQuery("logo_source").boost(15.0f));
        // 设置最小匹配数为0，确保不满足条件时不会影响其他评分
        webInfoQueryBuilder.minimumShouldMatch(0);
        boolQuery.should(webInfoQueryBuilder);
        
        return true;
    }

    @PostConstruct
    @Override
    public void init() {
        libraryInfoServiceMap.put(EsIndexEnum.COMPANY.getEsIndex(), this);
        libraryInfoServiceMap.put(EsIndexEnum.COMPANY.getIndex(), this);
    }

    @Override
    public void specialBuildBoolQuery(final BoolQueryBuilder boolQuery, final Map<String, List<String>> termQueries, final GeoQueryBO geoQuery) {
        generateGeoQuery(boolQuery, geoQuery);
        generateChainQuery(boolQuery, termQueries);
        generateLabelsQuery(boolQuery, termQueries);
        generateChainNodeQuery(boolQuery, termQueries);
        boolQuery.filter(QueryBuilders.existsQuery("name"));
    }

    /**
     * 生成标签查询条件
     */
    private void generateLabelsQuery(final BoolQueryBuilder boolQuery, final Map<String, List<String>> termQueries) {
        if (termQueries != null && termQueries.containsKey("labels")) {
            final List<String> labels = termQueries.get("labels");
            if (CollUtil.isNotEmpty(labels)) {
                boolQuery.filter(QueryBuilders.termsQuery("tags.name", labels));
            }
            termQueries.remove("labels");
        }
        if (termQueries != null && termQueries.containsKey("plate")) {
            final List<String> plate = termQueries.get("plate");
            if (CollUtil.isNotEmpty(plate)) {
                boolQuery.filter(QueryBuilders.termsQuery("tags.name", plate));
            }
            termQueries.remove("plate");
        }
        if (termQueries != null && termQueries.containsKey("capital")) {
            final List<String> capital = termQueries.get("capital");
            if (CollUtil.isNotEmpty(capital)) {
                final List<CompanyTagSchema> list = companyTagSchemaService.list(Wrappers.<CompanyTagSchema>lambdaQuery()
                        .in(CompanyTagSchema::getParentId, capital)
                        .or()
                        .in(CompanyTagSchema::getId, capital));
                boolQuery.filter(QueryBuilders.termsQuery("tags.id", list.stream().map(CompanyTagSchema::getId)
                        .collect(Collectors.toList())));
            }
            termQueries.remove("capital");
        }
        if (termQueries != null && termQueries.containsKey("rank")) {
            final List<String> rank = termQueries.get("rank");
            if (CollUtil.isNotEmpty(rank)) {
                final List<CompanyTagSchema> list = companyTagSchemaService.list(Wrappers.<CompanyTagSchema>lambdaQuery()
                        .in(CompanyTagSchema::getParentId, rank)
                        .or()
                        .in(CompanyTagSchema::getId, rank));
                boolQuery.filter(QueryBuilders.termsQuery("tags.id", list.stream().map(CompanyTagSchema::getId)
                        .collect(Collectors.toList())));
            }
            termQueries.remove("rank");
        }

    }

    /**
     * 根据地理位置条件生成查询参数
     */
    private void generateGeoQuery(final BoolQueryBuilder boolQuery, final GeoQueryBO geoQuery) {
        if (geoQuery != null) {
            //构建半径查询 GeoDistanceQueryBuilder
            final GeoDistanceQueryBuilder geoDistanceQueryBuilder = QueryBuilders.geoDistanceQuery("location")
                    .point(Double.parseDouble(geoQuery.getLatitude()), Double.parseDouble(geoQuery.getLongitude()))
                    .distance(geoQuery.getRadius(), DistanceUnit.KILOMETERS)
                    .geoDistance(GeoDistance.PLANE);
            boolQuery.filter(geoDistanceQueryBuilder);
        }
    }

    @Override
    protected void specialSearchSourceForLibrarySearch(final String esIndex, final SearchSourceBuilder searchSource,
                                                       final Map<String, List<String>> termQueries, final String keyword, final BoolQueryBuilder boolQuery) {

    }

    @Override
    public String specialSortForLibrarySearch(final BoolQueryBuilder boolQuery, final String sort, final String keyword) {
        // 未做搜索的时候企业库增加经营评价降序排列
        if (StrUtil.isEmpty(keyword)) {
            if (StrUtil.isBlank(sort)) {
                return "business_score:desc";
            }
        }
        return sort;
    }

    @Override
    public EsPageResult specialDealWithLibraryPageResult(final EsPageResult pageResult) {
        final List<Map<String, Object>> list = pageResult.getList();
        if (CollUtil.isEmpty(list)) {
            return pageResult;
        }
        final List<String> companyIdList = new ArrayList<>(list.size());
        for (final Map<String, Object> sourceAsMap : list) {
            companyIdList.add((String) sourceAsMap.get("id"));
            final Object tags = sourceAsMap.get("tags");
            if (tags instanceof List) {
                final Set<String> labelSet = new HashSet<>();
                ((List) tags).forEach(
                        tag -> {
                            if (tag instanceof Map) {
                                final Object tagObject = ((Map) tag).get("name");
                                if (tagObject != null) {
                                    labelSet.add((String) tagObject);
                                }
                            }
                        }
                );
                sourceAsMap.remove("tags");
                sourceAsMap.put("labels", labelSet);
            }
            sourceAsMap.put("region", Constants.getBelongArea((String) sourceAsMap.get("province"), (String) sourceAsMap.get("city"), null));
        }
        // 企业库额外查询字段
        final List<Company> companies = companyService.listByIds(companyIdList);
        final Map<String, Company> map = companies.stream().collect(Collectors.toMap(Company::getId, Function.identity(), (v1, v2) -> v1));
        final List<CompletableFuture<Void>> futureList = new ArrayList<>(list.size());

        // 检查是否为导出请求
        final boolean isForExport;
        Object requestContext = pageResult.getRequestContext();
        if (requestContext instanceof MultidimensionalQuery) {
            MultidimensionalQuery query = (MultidimensionalQuery) requestContext;
            isForExport = Boolean.TRUE.equals(query.getIsForExport());
        } else {
            isForExport = false;
        }

        for (final Map<String, Object> sourceAsMap : list) {
            futureList.add(CompletableFuture.runAsync(() -> {
                if (map.containsKey((String) sourceAsMap.get("id"))) {
                    final String id = (String) sourceAsMap.get("id");
                    final Company company = map.get(id);
                    
                    // 普通查询时只返回基本字段
                    if (!isForExport) {
                        sourceAsMap.put("regist_capi", company.getRegistCapi());
                        sourceAsMap.put("logo_source", company.getLogoSource());
                        sourceAsMap.put("address", company.getAddress());
                        sourceAsMap.put("tel", company.getTel());
                        sourceAsMap.put("website", company.getWebsite());
                    } else {
                        // 导出时返回所有需要的字段
                        sourceAsMap.put("name_en", company.getNameEn());
                        sourceAsMap.put("used_name", company.getUsedName());
                        sourceAsMap.put("credit_code", company.getCreditCode());
                        sourceAsMap.put("legal_person", company.getLegalPerson());
                        sourceAsMap.put("logo", company.getLogo());
                        sourceAsMap.put("logo_source", company.getLogoSource());
                        sourceAsMap.put("employee_size", company.getEmployeeSize());
                        sourceAsMap.put("insured_number", company.getInsuredNumber());
                        // valid_phone field exists in the database but not in the entity class
                        // sourceAsMap.put("valid_phone", company.getValidPhone());
                        sourceAsMap.put("tel", company.getTel());
                        sourceAsMap.put("email", company.getEmail());
                        sourceAsMap.put("website", company.getWebsite());
                        sourceAsMap.put("address", company.getAddress());
                        // contact_address field exists in the database but not in the entity class
                        // sourceAsMap.put("contact_address", company.getContactAddress());
                        sourceAsMap.put("establish_date", company.getEstablishDate());
                        sourceAsMap.put("nation", company.getNation());
                        sourceAsMap.put("province", company.getProvince());
                        sourceAsMap.put("city", company.getCity());
                        sourceAsMap.put("area", company.getArea());
                        sourceAsMap.put("town", company.getTown());
                        sourceAsMap.put("nation_code", company.getNationCode());
                        sourceAsMap.put("province_code", company.getProvinceCode());
                        sourceAsMap.put("city_code", company.getCityCode());
                        sourceAsMap.put("area_code", company.getAreaCode());
                        sourceAsMap.put("lng", company.getLng());
                        sourceAsMap.put("lat", company.getLat());
                        sourceAsMap.put("check_date", company.getCheckDate());
                        sourceAsMap.put("status", company.getStatus());
                        sourceAsMap.put("pay_capi", company.getPayCapi());
                        sourceAsMap.put("pay_capi_value", company.getPayCapiValue());
                        sourceAsMap.put("pay_capi_unit", company.getPayCapiUnit());
                        sourceAsMap.put("pay_capi_value_cal", company.getPayCapiValueCal());
                        sourceAsMap.put("regist_capi", company.getRegistCapi());
                        sourceAsMap.put("regist_capi_value", company.getRegistCapiValue());
                        sourceAsMap.put("regist_capi_unit", company.getRegistCapiUnit());
                        sourceAsMap.put("regist_capi_value_cal", company.getRegistCapiValueCal());
                        sourceAsMap.put("org_no", company.getOrgNo());
                        sourceAsMap.put("company_type", company.getCompanyType());
                        sourceAsMap.put("company_scale", company.getCompanyScale());
                        sourceAsMap.put("business_term", company.getBusinessTerm());
                        sourceAsMap.put("taxpayer_no", company.getTaxpayerNo());
                        sourceAsMap.put("nation_industry_1", company.getNationIndustry1());
                        sourceAsMap.put("nation_industry_2", company.getNationIndustry2());
                        sourceAsMap.put("nation_industry_3", company.getNationIndustry3());
                        sourceAsMap.put("nation_industry_4", company.getNationIndustry4());
                        sourceAsMap.put("nation_industry_code", company.getNationIndustryCode());
                        sourceAsMap.put("belong_org", company.getBelongOrg());
                        sourceAsMap.put("registration_id", company.getRegistrationId());
                        sourceAsMap.put("business_scope", company.getBusinessScope());
                        sourceAsMap.put("description", company.getDescription());
                    }
                }
            }, docExecutor));
        }

        final CompletableFuture<Void> allFutures =
                CompletableFuture.allOf(futureList.toArray(new CompletableFuture[list.size()]));
        try {
            allFutures.get();
        } catch (final Exception e) {
            log.error(e.getMessage(), e);
        }

        dealWithSecName(list);
        dealWithFecName(list);
        dealWithNecName(list);
        return pageResult;
    }

    @Override
    public File icdDataExport(final EsPageResult esPageResult, final HttpServletResponse response) throws IOException {
        // 构建数据
        final List<Map<String, Object>> list = esPageResult.getList();
        final List<IcdLibraryCompanyListExport> dataList = new ArrayList<>();
        for (final Map<String, Object> sourceAsMap : list) {
            final IcdLibraryCompanyListExport export = new IcdLibraryCompanyListExport();
            export.setName((String) sourceAsMap.get("name"));
            final Object labels = sourceAsMap.get("labels");
            if (labels instanceof List) {
                export.setLabels(StrUtil.join(";", (List<String>) labels));
            } else if (labels instanceof Set) {
                export.setLabels(StrUtil.join(";", (Set<String>) labels));
            }
            export.setLegalPerson((String) sourceAsMap.get("legal_person"));
            export.setEstablishDate((String) sourceAsMap.get("establish_date"));
            export.setRegistCapi((String) sourceAsMap.get("regist_capi"));
            export.setRegion(Constants.getBelongArea((String) sourceAsMap.get("province"), (String) sourceAsMap.get("city"), null));
            dataList.add(export);
        }
        return listExport(dataList, response, "企业明细导出表", IcdLibraryCompanyListExport.class);
    }

    @Override
    public File ipcDataExport(final EsPageResult esPageResult, final HttpServletResponse response) throws IOException {
        // 构建数据
        final List<Map<String, Object>> list = esPageResult.getList();
        final List<IpcLibraryCompanyListExport> dataList = new ArrayList<>();
        for (final Map<String, Object> sourceAsMap : list) {
            final IpcLibraryCompanyListExport export = new IpcLibraryCompanyListExport();
            export.setName((String) sourceAsMap.get("name"));
            final Object labels = sourceAsMap.get("labels");
            if (labels instanceof List) {
                export.setLabels(StrUtil.join(";", (List<String>) labels));
            } else if (labels instanceof Set) {
                export.setLabels(StrUtil.join(";", (Set<String>) labels));
            }
            export.setLegalPerson((String) sourceAsMap.get("legal_person"));
            export.setEstablishDate((String) sourceAsMap.get("establish_date"));
            export.setRegistCapi((String) sourceAsMap.get("regist_capi"));
            export.setRegion(Constants.getBelongArea((String) sourceAsMap.get("province"), (String) sourceAsMap.get("city"), null));
            export.setPatentOwnershipCount(String.valueOf(sourceAsMap.get("patentOwnershipCount")));
            dataList.add(export);
        }
        return listExport(dataList, response, "企业明细导出表", IpcLibraryCompanyListExport.class);
    }

}