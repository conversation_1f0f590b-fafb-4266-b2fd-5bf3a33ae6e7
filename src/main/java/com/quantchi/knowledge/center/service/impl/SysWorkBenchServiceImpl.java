package com.quantchi.knowledge.center.service.impl;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.TimeInterval;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.http.HtmlUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.deepoove.poi.XWPFTemplate;
import com.deepoove.poi.data.*;
import com.deepoove.poi.data.style.BorderStyle;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.quantchi.knowledge.center.bean.bo.*;
import com.quantchi.knowledge.center.bean.entity.*;
import com.quantchi.knowledge.center.bean.enums.EsIndexEnum;
import com.quantchi.knowledge.center.bean.enums.DownloadTypeEnum;
import com.quantchi.knowledge.center.bean.exception.BusinessException;
import com.quantchi.knowledge.center.bean.model.*;
import com.quantchi.knowledge.center.bean.vo.*;
import com.quantchi.knowledge.center.dao.mysql.*;
import com.quantchi.knowledge.center.helper.ElasticsearchHelper;
import com.quantchi.knowledge.center.service.*;
import com.quantchi.knowledge.center.service.index.NewsService;
import com.quantchi.knowledge.center.util.LocalFileUtil;
import com.quantchi.knowledge.center.util.Word2PdfUtil;
import com.quantchi.knowledge.center.service.IDataExportService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.docx4j.Docx4J;
import org.docx4j.fonts.IdentityPlusMapper;
import org.docx4j.fonts.Mapper;
import org.docx4j.fonts.PhysicalFonts;
import org.docx4j.openpackaging.packages.WordprocessingMLPackage;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.SearchHits;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.multipart.MultipartFile;

import javax.xml.XMLConstants;
import javax.xml.transform.TransformerFactory;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

import static com.quantchi.knowledge.center.controller.IndustryLinkAnalysisController.SCHEMA_ICD;
import static com.quantchi.knowledge.center.controller.PoiController.REPORT_FILE_MODULE_NAME;

@Service
@Slf4j
@RequiredArgsConstructor
public class SysWorkBenchServiceImpl implements SysWorkBenchService {

    private final SysUserCollectionMapper sysUserCollectionMapper;
    private final SchemaSecMapper schemaSecMapper;
    private final SchemaFecMapper schemaFecMapper;
    private final ElasticsearchHelper elasticsearchHelper;
    private final SysUserFollowMapper sysUserFollowMapper;
    private final SysUserMaterialMapper sysUserMaterialMapper;
    private final IndustryChainMapper industryChainMapper;
    private final SchemaIcdMapper schemaIcdMapper;
    private final NewsService newsService;
    private final SchemaIpcMapper schemaIpcMapper;
    private final ISysUserDownloadService sysUserDownloadService;
    private final SysUserDownloadMapper sysUserDownloadMapper;
    private final ISysReportService sysReportService;
    private final PatentPortraitService patentPortraitService;
    private final IPatentDimensionService patentDimensionService;
    private final PatentEsInfoService patentEsInfoService;
    private final ThreadPoolTaskExecutor docExecutor;
    private final IndustryOverviewService industryOverviewService;
    private final TechnologyPortraitService technologyPortraitService;
    private final LibraryInfoService libraryInfoService;
    private final ISysFileService sysFileService;
    private final IDataExportService dataExportService;

    @Override
    public String collect(final WorkBenchCollectionQuery query) {
        final SysUserCollection collection = new SysUserCollection();
        collection.setUserId(query.getUserId());
        collection.setInfoId(query.getInfoId());
        collection.setType(query.getType());
        collection.setStatus(1);
        String index = "";
        switch (query.getType()) {
            case "资讯":
                index = EsIndexEnum.NEWS.getEsIndex();
                break;
            case "政策":
                index = EsIndexEnum.POLICY.getEsIndex();
                break;
            case "研报":
                index = EsIndexEnum.REPORT.getEsIndex();
                break;
        }
        final Map<String, Object> source = elasticsearchHelper.getDataById(index, query.getInfoId(), new String[]{"id", "title"}, new String[]{});
        if (source != null) {
            collection.setTitle((String) source.get("title"));
        }
        try {
            final int isSuccess = sysUserCollectionMapper.collectExistUpdateOrElseInsert(collection);
            if (isSuccess <= 0) {
                return "收藏失败";
            }
        } catch (final Exception e) {
            log.error("收藏失败", e);
            return "收藏失败";
        }

        return "收藏成功";
    }

    @Override
    public String cancelCollect(final WorkBenchCollectionQuery query) {
        try {
            final int isSuccess = sysUserCollectionMapper.cancelCollect(query);
            if (isSuccess <= 0) {
                return "取消收藏失败";
            }
        } catch (final Exception e) {
            log.error("取消收藏失败", e);
            return "取消收藏失败";
        }

        return "取消收藏成功";
    }

    @Override
    public CommonDataForEsVO collectList(final Long userId, final String type, final String keyword, final Integer pageNum, final Integer pageSize) throws IOException {
        final CommonDataForEsVO result = new CommonDataForEsVO();

        final Integer index = (pageNum - 1) * pageSize;
        final List<String> infoIdList = sysUserCollectionMapper.getInfoIdListByUserIdAndType(userId, type, keyword, index, pageSize);
        final Integer total = sysUserCollectionMapper.getInfoIdListTotalByUserIdAndType(userId, type, keyword);

        result.setTotal(total);
        //根据id取详细数据
        if (!CollectionUtils.isEmpty(infoIdList)) {
            final SearchRequest searchRequest;
            final SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
            final BoolQueryBuilder boolQueryBuilder = new BoolQueryBuilder();
            if ("资讯".equals(type)) {
                searchRequest = new SearchRequest(EsIndexEnum.NEWS.getEsIndex());
                searchSourceBuilder.fetchSource(new String[]{"id", "title", "publish_date", "industry", "source", "sentiment", "tags", "url", "abstract", "sec", "fec"}, null);
            } else if ("政策".equals(type)) {
                searchRequest = new SearchRequest(EsIndexEnum.POLICY.getEsIndex());
                searchSourceBuilder.fetchSource(new String[]{"id", "title", "publish_date", "industry", "source", "content", "type", "url", "level", "sec", "fec"}, null);
            } else {
                //研报
                searchRequest = new SearchRequest(EsIndexEnum.REPORT.getEsIndex());
                searchSourceBuilder.fetchSource(new String[]{"id", "title", "source", "publish_date", "authors", "industry", "type", "url", "sec", "fec", "institution", "html"}, null);
            }
            boolQueryBuilder.filter(QueryBuilders.termsQuery("id", infoIdList));
            searchSourceBuilder.query(boolQueryBuilder);
            searchRequest.source(searchSourceBuilder);
            final SearchResponse searchResponse = elasticsearchHelper.getRestHighLevelClient().search(searchRequest, RequestOptions.DEFAULT);
            final SearchHits hits = searchResponse.getHits();
            final List<Map<String, Object>> list = new ArrayList<>();
            for (final SearchHit hit : hits) {
                final Map<String, Object> sourceAsMap = hit.getSourceAsMap();
                newsService.dealWithTag(sourceAsMap);
                if ("研报".equals(type) && sourceAsMap.get("html") instanceof String) {
                    // 处理html格式的数据，去除标签
                    sourceAsMap.put("content", HtmlUtil.cleanHtmlTag((String) sourceAsMap.get("html")));
                    sourceAsMap.remove("html");
                } else if ("资讯".equals(type)) {
                    sourceAsMap.put("content", sourceAsMap.get("abstract"));
                }
                list.add(sourceAsMap);
            }

            result.setList(sourceForIndustry(list));
        }

        return result;
    }

    /**
     * 接受一个map列表作为输入，并返回同样的列表，但每个map中都增加了一个新的键值对，键为"industry"，值为一个列表。这个列表包含了行业名称，这些名称是通过将"sec"和"fec"的ID映射到对应的名称来获取的
     *
     * @param list
     * @return
     */
    @Override
    public List<Map<String, Object>> sourceForIndustry(final List<Map<String, Object>> list) {
        // 更换显示的标签
        final List<String> secIdList = new ArrayList<>(list.size());
        final List<String> fecIdList = new ArrayList<>(list.size());
        list.forEach(source -> {
            final Object sec = source.get("sec");
            if (sec instanceof List && CollUtil.isNotEmpty((List) sec)) {
                final List<String> secList = (List<String>) sec;
                secIdList.add(secList.get(0));
            }
            // 未来产业分类
            final Object fec = source.get("fec");
            if (fec instanceof List && CollUtil.isNotEmpty((List) fec)) {
                final List<String> fecList = (List<String>) fec;
                fecIdList.add(fecList.get(0));
            }
        });
        final Map<String, String> secMap;
        if (CollUtil.isNotEmpty(secIdList)) {
            final List<SchemaSec> schemaSecs = schemaSecMapper.selectList(Wrappers.<SchemaSec>lambdaQuery()
                    .select(SchemaSec::getId, SchemaSec::getName)
                    .in(SchemaSec::getId, secIdList));
            secMap = schemaSecs.stream().collect(Collectors.toMap(SchemaSec::getId, SchemaSec::getName));
        } else {
            secMap = new HashMap<>();
        }
        final Map<String, String> fecMap;
        if (CollUtil.isNotEmpty(fecIdList)) {
            final List<SchemaFec> schemaFecs = schemaFecMapper.selectList(Wrappers.<SchemaFec>lambdaQuery()
                    .select(SchemaFec::getId, SchemaFec::getName)
                    .in(SchemaFec::getId, fecIdList));
            fecMap = schemaFecs.stream().collect(Collectors.toMap(SchemaFec::getId, SchemaFec::getName));
        } else {
            fecMap = new HashMap<>();
        }
        list.forEach(source -> {
            final List<String> tagList = new ArrayList<>();
            final Object sec = source.get("sec");
            if (sec instanceof List && CollUtil.isNotEmpty((List) sec)) {
                final List<String> secList = (List<String>) sec;
                final String secName = secMap.getOrDefault(secList.get(0), "");
                tagList.add(secName);
            }
            // 未来产业分类
            final Object fec = source.get("fec");
            if (fec instanceof List && CollUtil.isNotEmpty((List) fec)) {
                final List<String> fecList = (List<String>) fec;
                final String fecName = fecMap.getOrDefault(fecList.get(0), "");
                tagList.add(fecName);
            }
            source.put("industry", tagList);
        });

        return list;
    }

    /**
     * 根据用户id、数据idList 查询收藏状态
     * 只会返回正在收藏的数据id和状态（收藏ing status = 1）
     * 未在表中or收藏又取消的数据不返回，即未收藏
     *
     * @param userId
     * @param type
     * @param infoIdList
     * @return
     */
    public List<CollectionStatusBO> selectCollectStatusById(final Long userId, final String type, final List<String> infoIdList) {
        return sysUserCollectionMapper.selectCollectStatusById(userId, type, infoIdList);
    }

    @Override
    public String follow(final WorkBenchFollowQuery query) {
        final SysUserFollow follow = new SysUserFollow();
        follow.setUserId(query.getUserId());
        follow.setInfoId(query.getInfoId());
        follow.setType(query.getType());
        follow.setStatus(1);
        if ("企业".equals(query.getType())) {
            final Map<String, Object> source = elasticsearchHelper.getDataById(EsIndexEnum.COMPANY.getEsIndex(), query.getInfoId(), new String[]{"id", "name", "credit_code"}, new String[]{});
            if (source != null) {
                follow.setName((String) source.get("name"));
                follow.setCreditCode((String) source.get("credit_code"));
            }
        }
        try {
            final int isSuccess = sysUserFollowMapper.followExistUpdateOrElseInsert(follow);
            if (isSuccess <= 0) {
                return "关注失败";
            }
        } catch (final Exception e) {
            log.error("关注失败", e);
            return "关注失败";
        }

        return "关注成功";
    }

    @Override
    public String cancelFollow(final WorkBenchFollowQuery query) {
        try {
            final int isSuccess = sysUserFollowMapper.cancelFollow(query);
            if (isSuccess <= 0) {
                return "取消关注失败";
            }
        } catch (final Exception e) {
            log.error("取消关注失败", e);
            return "取消关注失败";
        }

        return "取消关注成功";
    }

    @Override
    public SysFollowListVO followList(final Long userId, final String type, final String keyword, final Integer pageNum, final Integer pageSize) {
        final SysFollowListVO result = new SysFollowListVO();

        final Integer index = (pageNum - 1) * pageSize;
        final List<SysUserFollowBO> list = sysUserFollowMapper.getInfoIdListByUserIdAndType(userId, type, keyword, index, pageSize);
        final Integer total = sysUserFollowMapper.getInfoIdListTotalByUserIdAndType(userId, type, keyword);

        result.setList(list);
        result.setTotal(total);
        return result;
    }

    /**
     * 查询关注状态
     * 只返回正在关注的关联id
     *
     * @param type
     * @param infoIdList
     * @return
     */
    public List<String> selectFollowStatusById(final String type, final List<String> infoIdList) {
        if (!StpUtil.isLogin()) {
            return null;
        }
        if (CollectionUtils.isEmpty(infoIdList)) {
            return null;
        }
        final Long userId = StpUtil.getLoginIdAsLong();
        return sysUserFollowMapper.selectFollowStatusById(userId, type, infoIdList);
    }

    @Override
    public SysFollowStatisticVO followStatistic(final Long userId, final String type) throws IOException {
        final SysFollowStatisticVO result = new SysFollowStatisticVO();

        //关注数量
        final Integer total = sysUserFollowMapper.getInfoIdListTotalByUserIdAndType(userId, type, null);
        result.setTotal(total);

        //近30天新增走势 按周聚合
        final List<CommonDataForCountBO> trendList = sysUserFollowMapper.trendFor30DaysByWeeks(userId, type);
        result.setIncreasedTrendList(completeWeeks(trendList));//无数据周补全
        return result;
    }

    public List<CommonDataForCountBO> completeWeeks(final List<CommonDataForCountBO> list) {
        // 创建一个Map来存储周的计数
        final Map<String, Integer> weekCountMap = new HashMap<>();
        for (final CommonDataForCountBO bo : list) {
            weekCountMap.put(bo.getName(), bo.getCount());
        }

        // 创建一个过去30天的所有周的列表
        final List<String> weeksList = new ArrayList<>();
        final SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-w周");
        final Calendar cal = Calendar.getInstance();
        cal.add(Calendar.DATE, -30); // 从今天开始减去30天
        while (cal.getTime().before(new Date())) {
            final String week = dateFormat.format(cal.getTime());
            weeksList.add(week);
            cal.add(Calendar.WEEK_OF_YEAR, 1); // 加一周
        }

        // 创建结果列表，并确保所有周都有数据，没有数据的周计数为0
        final List<CommonDataForCountBO> finalResults = new ArrayList<>();
        for (final String week : weeksList) {
            final CommonDataForCountBO bo = new CommonDataForCountBO();
            bo.setName(week);
            bo.setCount(weekCountMap.getOrDefault(week, 0)); // 如果没有数据，则默认为0
            finalResults.add(bo);
        }

        return finalResults;
    }

    @Override
    public String saveMaterial(final MultipartFile file, final Long userId, final Integer type, final String infoId, final String sectorName) {
        if (userId == null) {
            return "用户id不能为空";
        }
        if (type == null) {
            return "页面类型不能为空";
        }
        if (StringUtils.isEmpty(sectorName)) {
            return "板块名称不能为空";
        }
        final SysFileVO sysFileVO = sysFileService.uploadBlobFile(file);

        if (sysFileVO != null) {
            final SysUserMaterial sysUserMaterial = new SysUserMaterial();
            //入库
            String name = getMaterialName(type, infoId);

            final Date now = new Date();//时间
            final SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
            final String dateStr = sdf.format(now);
            name = name + "-" + sectorName + "_" + dateStr;//素材命名
            sysUserMaterial.setUserId(userId);
            sysUserMaterial.setName(name);
            sysUserMaterial.setFileName(sysFileVO.getFileName());
            sysUserMaterial.setUrl(sysFileVO.getUrl());
            sysUserMaterial.setCreateTime(now);
            // 保存infoId、类型和板块名称
            sysUserMaterial.setInfoId(infoId);
            sysUserMaterial.setType(type);
            sysUserMaterial.setSectorName(sectorName);
            log.info("sysUserMaterial={}", JSON.toJSONString(sysUserMaterial));

            try {
                final int isSuccess = sysUserMaterialMapper.insert(sysUserMaterial);
                if (isSuccess <= 0) {
                    return "素材保存失败";
                }
            } catch (final Exception e) {
                log.error("素材保存失败", e);
                return "素材保存失败";
            }
        }

        return "素材保存成功";
    }

    private String getMaterialName(final Integer type, final String infoId) {
        //入库
        String name = "";
        switch (type) {
            case 1:
                name = "产业链分析";
                final Knowledge chain = industryChainMapper.selectById(infoId);
                if (chain != null) {
                    name = name + "-" + chain.getName();
                }
                break;
            case 2:
                name = "产业环节分析";
                final SchemaIcd icd = schemaIcdMapper.selectById(infoId);
                if (icd != null) {
                    name = name + "-" + icd.getName();
                }
                break;
            case 3:
                name = "技术画像";
                final String technology = schemaIpcMapper.getTechnologyByCode(infoId);
                name = name + "-" + technology;
            default:
                break;
        }
        return name;
    }

    @Override
    public String editMaterialName(final Long id, final String newName) {
        final SysUserMaterial sysUserMaterial = new SysUserMaterial();
        sysUserMaterial.setId(id);
        sysUserMaterial.setName(newName);

        try {
            final int isSuccess = sysUserMaterialMapper.updateById(sysUserMaterial);
            if (isSuccess <= 0) {
                return "编辑素材名称失败";
            }
        } catch (final Exception e) {
            log.error("编辑素材名称失败", e);
            return "编辑素材名称失败";
        }

        return "编辑素材名称成功";
    }

    @Override
    public String deleteMaterial(final Long id) {
        final SysUserMaterial sysUserMaterial = sysUserMaterialMapper.selectById(id);
        if (sysUserMaterial == null) {
            return "该素材不存在";
        }

        //先删除oss文件
        final boolean deleteFile = sysFileService.deleteFile(sysUserMaterial.getFileName());
        if (!deleteFile) {
            return "OSS删除失败";
        }
        try {
            final int isSuccess = sysUserMaterialMapper.deleteById(sysUserMaterial.getId());
            if (isSuccess <= 0) {
                return "素材删除失败";
            }
        } catch (final Exception e) {
            log.error("素材删除失败", e);
            return "素材删除失败";
        }

        return "素材删除成功";
    }

    @Override
    public SysMaterialListVO materialList(final Long userId, final String keyword, final Integer pageNum, final Integer pageSize) {
        final SysMaterialListVO result = new SysMaterialListVO();
        final Integer index = (pageNum - 1) * pageSize;
        final List<SysMaterialListBO> list = sysUserMaterialMapper.getMaterialList(userId, keyword, index, pageSize);
        final Integer total = sysUserMaterialMapper.getMaterialListTotal(userId, keyword);
        list.forEach(item -> {
            item.setImgUrl(item.getUrl());
        });
        result.setList(list);
        result.setTotal(total);
        return result;
    }

    /**
     * 查询用户收藏的模块信息
     *
     * @param userId 用户ID
     * @param type 类型：1-产业链分析，2-产业环节分析，3-技术画像
     * @return 用户收藏的模块信息
     */
    @Override
    public List<UserMaterialModuleVO.ModuleInfo> getUserMaterialModules(final Long userId, final Integer type, final String infoId) {
        if (userId == null) {
            throw new IllegalArgumentException("用户ID不能为空");
        }
        if (type == null) {
            throw new IllegalArgumentException("类型不能为空");
        }

        return sysUserMaterialMapper.getUserMaterialModules(userId, type, infoId);
    }

    /**
     * 下载埋点
     */
    @Override
    public Integer downloadRecord(final MultipartFile file,
                                  final String infoId,
                                  final Integer type,
                                  final String sectorName) {
        final SysFileVO sysFileVO = sysFileService.uploadBlobFile(file);
        final long userId = StpUtil.getLoginIdAsLong();
        return sysUserDownloadService.saveDownloadRecord(sysFileVO, getMaterialName(type, infoId) + "-" + sectorName, userId);
    }

    /**
     * 报告或数据下载接口，下载后生成下载记录，到我的下载页面查看
     */
    @Override
    public Integer reportOrDataDownload(final ReportOrDataDownloadBO downloadBO) throws Exception {
        final String module = downloadBO.getModule();
        final Object query = downloadBO.getQuery();
        final long userId = StpUtil.getLoginIdAsLong();
        // 生成下载记录
        final SysUserDownload sysUserDownload = new SysUserDownload();
        sysUserDownload.setUserId(StpUtil.getLoginIdAsLong());
        sysUserDownload.setModule(module);
        sysUserDownload.setName(module);
        sysUserDownload.setStatus(0);
        sysUserDownloadService.save(sysUserDownload);
        final Long sysUserDownloadId = sysUserDownload.getId();
        // 从主线程获取用户数据 放到局部变量中
        final RequestAttributes attributes = RequestContextHolder.getRequestAttributes();
        docExecutor.execute(() -> {
            // 把旧RequestAttributes放到新线程的RequestContextHolder中
            RequestContextHolder.setRequestAttributes(attributes);
            final ReportDownloadBO reportDownloadBO;
            final IndustryRankListQuery industryRankListQuery;
            final SysFileVO sysFileVO;
            final File tempFile;
            final EsPageResult esPageResult;
            try {
                switch (module) {
                    case "产业探查-产业画像-生成报告":
                    case "产业探查-产业节点画像-生成报告":
                    case "产业探查-技术画像-生成报告":
                    case "产业探查-企业画像-生成报告":
                    case "报告中心-智能报告-产业报告-产业链分析洞察报告":
                    case "报告中心-智能报告-产业报告-产业画像分析报告":
                    case "报告中心-智能报告-技术报告-技术画像分析报告":
                    case "报告中心-智能报告-企业报告-企业画像分析报告":
                        reportDownloadBO = JSONObject.parseObject(JSON.toJSONString(query), ReportDownloadBO.class);
                        sysReportService.downloadAIReport(reportDownloadBO, sysUserDownloadId, userId);
                        break;
                    case "产业探查-专利画像-下载报告":
                    case "报告中心-智能报告-技术报告-专利画像分析报告":
                        generatePatentReport(query.toString(), sysUserDownloadId, userId);
                        break;
                    case "产业探查-产业画像-产业榜单-下载":
                        // 添加导出限制检查
                        dataExportService.checkExportLimit();
                        industryRankListQuery = JSONObject.parseObject(JSON.toJSONString(query), IndustryRankListQuery.class);
                        tempFile = industryOverviewService.industryRankListExport(industryRankListQuery, null, sysUserDownloadId);
                        sysFileVO = sysFileService.uploadFile(tempFile, tempFile.getName(), LocalFileUtil.getFileExtension(tempFile.getName()));
                        sysUserDownloadService.updateDownloadRecord(sysFileVO, "产业链榜单导出表", userId, sysUserDownloadId, DownloadTypeEnum.DATA_DOWNLOAD.getId());
                        break;
                    case "产业探查-产业节点画像-产业榜单-下载":
                        // 添加导出限制检查
                        dataExportService.checkExportLimit();
                        industryRankListQuery = JSONObject.parseObject(JSON.toJSONString(query), IndustryRankListQuery.class);
                        final String chainId = industryRankListQuery.getChainId();
                        if (CharSequenceUtil.isBlank(chainId)) {
                            industryRankListQuery.setChainId(SCHEMA_ICD);
                        }
                        industryOverviewService.industryRankListExport(industryRankListQuery, null, sysUserDownloadId);
                        tempFile = industryOverviewService.industryRankListExport(industryRankListQuery, null, sysUserDownloadId);
                        sysFileVO = sysFileService.uploadFile(tempFile, tempFile.getName(), LocalFileUtil.getFileExtension(tempFile.getName()));
                        sysUserDownloadService.updateDownloadRecord(sysFileVO, "产业节点榜单导出表", userId, sysUserDownloadId, DownloadTypeEnum.DATA_DOWNLOAD.getId());
                        break;
                    case "产业探查-产业节点画像-高被引用专利排行TOP10-下载":
                        tempFile = technologyPortraitService.exportKeyPatentList(downloadBO.getChainId(), query.toString(), null);
                        sysFileVO = sysFileService.uploadFile(tempFile, tempFile.getName(), LocalFileUtil.getFileExtension(tempFile.getName()));
                        sysUserDownloadService.updateDownloadRecord(sysFileVO, "高被引用专利排行TOP10", userId, sysUserDownloadId, DownloadTypeEnum.DATA_DOWNLOAD.getId());
                        break;
                    case "产业探查-产业节点画像-产业数据-下载":
                        // 添加导出限制检查
                        dataExportService.checkExportLimit();
                        final IcdDataQuery icdDataQuery = JSONObject.parseObject(JSON.toJSONString(query), IcdDataQuery.class);
                        icdDataQuery.setPageNum(1);
                        icdDataQuery.setPageSize(5000);
                        icdDataQuery.setIsHighlight(false);
                        esPageResult = libraryInfoService.queryByTermsAndKeyForIcd(icdDataQuery);
                        tempFile = libraryInfoService.getService(EsIndexEnum.getEsIndexByType(icdDataQuery.getType())).icdDataExport(esPageResult, null);
                        sysFileVO = sysFileService.uploadFile(tempFile, tempFile.getName(), LocalFileUtil.getFileExtension(tempFile.getName()));
                        sysUserDownloadService.updateDownloadRecord(sysFileVO, "产业节点数据", userId, sysUserDownloadId, DownloadTypeEnum.DATA_DOWNLOAD.getId());
                        break;
                    case "产业探查-技术画像-技术数据-下载":
                        // 添加导出限制检查
                        dataExportService.checkExportLimit();
                        final IpcDataQuery ipcDataQuery = JSONObject.parseObject(JSON.toJSONString(query), IpcDataQuery.class);
                        ipcDataQuery.setPageNum(1);
                        ipcDataQuery.setPageSize(5000);
                        ipcDataQuery.setIsHighlight(false);
                        esPageResult = libraryInfoService.queryByTermsAndKeyForIpc(ipcDataQuery);
                        tempFile = libraryInfoService.getService(EsIndexEnum.getEsIndexByType(ipcDataQuery.getType())).ipcDataExport(esPageResult, null);
                        sysFileVO = sysFileService.uploadFile(tempFile, tempFile.getName(), LocalFileUtil.getFileExtension(tempFile.getName()));
                        sysUserDownloadService.updateDownloadRecord(sysFileVO, "技术数据", userId, sysUserDownloadId, DownloadTypeEnum.DATA_DOWNLOAD.getId());
                        break;
                }
            } catch (final Exception e) {
                log.error("生成报告失败", e);
                sysUserDownload.setStatus(2);
                sysUserDownloadMapper.updateById(sysUserDownload);
            }
        });
        return 1;
    }


    /**
     * 下载任务列表
     */
    @Override
    public PageInfo<SysUserDownload> downloadList(final String keyword, final Integer type, final Integer pageNum, final Integer pageSize) {
        PageHelper.startPage(pageNum, pageSize);
        final List<SysUserDownload> sysUserDownloads = sysUserDownloadMapper.selectList(Wrappers.<SysUserDownload>lambdaQuery()
                .eq(SysUserDownload::getUserId, StpUtil.getLoginIdAsLong())
                .eq(type != null, SysUserDownload::getType, type)
                .like(CharSequenceUtil.isNotBlank(keyword), SysUserDownload::getName, keyword)
                .eq(SysUserDownload::getIsValid, 1)
                .orderByDesc(SysUserDownload::getCreateTime));
        sysUserDownloads.forEach(sysUserDownload -> {
            if (sysUserDownload.getStatus() == null && sysUserDownload.getUrl() != null) {
                sysUserDownload.setStatus(1);
            }
        });
        return new PageInfo<>(sysUserDownloads);
    }

    @Override
    public Integer removeDownloadRecord(final Long id) {
        final SysUserDownload sysUserDownload = sysUserDownloadMapper.selectById(id);
        if (sysUserDownload == null) {
            throw new BusinessException("下载任务不存在");
        }
        sysUserDownload.setIsValid(0);
        sysUserDownloadMapper.updateById(sysUserDownload);
        docExecutor.execute(() -> {
            // 删除对应的oss文件
            if (sysUserDownload.getUrl() != null) {
                final SysFile sysFile = sysFileService.getOne(Wrappers.<SysFile>lambdaQuery()
                        .eq(SysFile::getUrl, sysUserDownload.getUrl()));
                if (sysFile != null) {
                    sysFileService.deleteFile(sysFile.getFileName());
                }
            }
        });
        return 1;
    }
    
    @Override
    public String cancelSaveMaterial(final Long userId, final Integer type, final String infoId, final String sectorName) {
        if (userId == null) {
            return "用户id不能为空";
        }
        if (type == null) {
            return "页面类型不能为空";
        }
        if (StringUtils.isEmpty(infoId)) {
            return "信息id不能为空";
        }
        if (StringUtils.isEmpty(sectorName)) {
            return "板块名称不能为空";
        }
        
        // 查找符合条件的素材记录
        final List<SysUserMaterial> materials = sysUserMaterialMapper.findByUserIdAndTypeAndInfoIdAndSectorName(
                userId, type, infoId, sectorName);
        
        if (CollectionUtils.isEmpty(materials)) {
            return "未找到对应的素材记录";
        }
        
        int successCount = 0;
        int failCount = 0;
        
        // 遍历处理每条符合条件的记录
        for (final SysUserMaterial material : materials) {
            try {
                // 先删除OSS文件
                final boolean deleteFile = sysFileService.deleteFile(material.getFileName());
                if (!deleteFile) {
                    log.error("OSS文件删除失败，文件名: {}", material.getFileName());
                    failCount++;
                    continue;
                }
                
                // 删除数据库记录
                final int isSuccess = sysUserMaterialMapper.deleteById(material.getId());
                if (isSuccess <= 0) {
                    log.error("素材记录删除失败，ID: {}", material.getId());
                    failCount++;
                } else {
                    successCount++;
                }
            } catch (final Exception e) {
                log.error("取消保存素材失败", e);
                failCount++;
            }
        }
        
        if (failCount > 0) {
            return String.format("部分素材取消保存失败：成功%d条，失败%d条", successCount, failCount);
        }
        
        return "素材取消保存成功";
    }

    @Override
    public File generatePatentReport(final String id, final Long sysUserDownloadId, final Long userId) throws Exception {
        TimeInterval timer = new TimeInterval();
        final Map<String, Object> dataMap = Collections.synchronizedMap(new HashMap<>());
        final AtomicReference<String> patentName = new AtomicReference<>(id);
        final List<CompletableFuture<Void>> futureList = new ArrayList<>(5);
        futureList.add(CompletableFuture.runAsync(() -> {
            try {
                patentName.set(setPatentOverview(id, dataMap));
            } catch (final IOException e) {
                log.error("setPatentOverview error", e);
            }
        }, docExecutor));
        futureList.add(CompletableFuture.runAsync(() -> {
            try {
                setValueEvaluation(id, dataMap);
            } catch (final IOException e) {
                e.printStackTrace();
            }
        }, docExecutor));
        futureList.add(CompletableFuture.runAsync(() -> {
            try {
                setCitedPatent(id, dataMap);
            } catch (final IOException e) {
                log.error("setCitedPatent error", e);
            }
        }, docExecutor));
        futureList.add(CompletableFuture.runAsync(() -> {
            try {
                setCognatePatent(id, dataMap);
            } catch (final IOException e) {
                log.error("setCognatePatent error", e);
            }
        }, docExecutor));
        futureList.add(CompletableFuture.runAsync(() -> {
            setSimilarPatent(id, dataMap);
        }, docExecutor));
        final CompletableFuture<Void> allFutures =
                CompletableFuture.allOf(futureList.toArray(new CompletableFuture[5]));
        try {
            allFutures.get();
        } catch (final Exception e) {
            log.error(e.getMessage(), e);
        }
        log.info("获取并填充分析报告数据耗时{}ms", timer.interval());
        timer = new TimeInterval();

        // 构建文件
        final String fileName = id + ".docx";
        final String pdfFileName = fileName.replace("docx", "pdf");
        final String wordPath = LocalFileUtil.getActualFilePath(REPORT_FILE_MODULE_NAME, fileName);
        final String targetPdfPath = wordPath.replace("docx", "pdf");

        // 使用 getResourceAsStream 加载模板
        final InputStream templateStream = this.getClass().getClassLoader().getResourceAsStream("template/patentTemplate.docx");

        if (templateStream != null) {
            XWPFTemplate.compile(templateStream)
                    .render(dataMap)
                    .writeToFile(wordPath);
        }
        log.info("word文件生成耗时{}ms", timer.interval());
        timer = new TimeInterval();
        // word转pdf
        Word2PdfUtil.doc2pdf(wordPath, targetPdfPath);
//        docxToPdf(wordPath, targetPdfPath);
        log.info("pdf文件生成耗时{}ms", timer.interval());
        // 将文件转成下载链接
        final File file = new File(targetPdfPath);
        if (!file.exists()) {
            throw new BusinessException("文件生成失败");
        }
        // 记录文件下载
        docExecutor.execute(() -> {
            final SysFileVO sysFileVO;
            try {
                sysFileVO = sysFileService.uploadFile(file, patentName.get() + ".pdf", "pdf");
            } catch (Exception e) {
                log.error("文件上传失败", e);
                return;
            }
            if (sysUserDownloadId != null) {
                sysUserDownloadService.updateDownloadRecord(sysFileVO, "专利画像分析报告-" + patentName.get(), userId, sysUserDownloadId, DownloadTypeEnum.REPORT_DOWNLOAD.getId());
            } else {
                sysUserDownloadService.saveDownloadRecord(sysFileVO, "专利画像分析报告-" + patentName.get(), userId);
            }
        });
        return file;
    }

    /**
     * 设置专利概览内容和技术分析内容
     *
     * @param id
     * @param dataMap
     * @throws IOException
     */
    public String setPatentOverview(final String id, final Map<String, Object> dataMap) throws IOException {
        final Map<String, Object> patentOverview = patentPortraitService.patentOverview(id);
        String patentName = (String) patentOverview.get("title_cn");
        //title_cn为空则取title_en,还为空则取title
        if (StringUtils.isEmpty(patentName)) {
            if (!StringUtils.isEmpty((String) patentOverview.get("title_en"))) {
                patentName = (String) patentOverview.get("title_en");
            } else {
                patentName = (String) patentOverview.get("title");
            }
        }
        // 首页
        dataMap.put("patentName", patentName);
        dataMap.put("public_code", patentOverview.get("public_code"));

        // 基本信息
        final Object applicants = patentOverview.get("applicants");
        final List<String> applicantsList = new ArrayList<>();
        if (applicants instanceof List) {
            ((List) applicants).forEach(item -> {
                if (item instanceof Map) {
                    applicantsList.add((String) ((Map) item).get("name"));
                }
            });
        }
        final Object applicantsCountry = patentOverview.get("applicants_country");
        final List<String> applicantsCountryList = new ArrayList<>();
        if (applicantsCountry instanceof List) {
            ((List) applicantsCountry).forEach(item -> {
                if (item instanceof String) {
                    applicantsCountryList.add((String) item);
                }
            });
        }
        final Object patentees = patentOverview.get("patentees");
        final List<String> patenteesList = new ArrayList<>();
        if (patentees instanceof List) {
            ((List) patentees).forEach(item -> {
                if (item instanceof Map) {
                    patenteesList.add((String) ((Map) item).get("name"));
                }
            });
        }
        final Object inventors = patentOverview.get("inventors");
        final List<String> inventorsList = new ArrayList<>();
        if (inventors instanceof List) {
            ((List) inventors).forEach(item -> {
                if (item instanceof Map) {
                    inventorsList.add((String) ((Map) item).get("name"));
                }
            });
        }
        final TableRenderData basicInfoTable = Tables.of(new String[][]{
                new String[]{"申请号", getObject(patentOverview.get("apply_code")), "申请日", getObject(patentOverview.get("apply_date"))},
                new String[]{"公开（公告）号", getObject(patentOverview.get("public_code")), "公开（公告）日", getObject(patentOverview.get("public_date"))},
                new String[]{"专利类型", getObject(patentOverview.get("patent_type")), "当前状态", getObject(patentOverview.get("status"))},
                new String[]{"申请人", String.join(",", applicantsList), "申请人国家/地区", String.join(",", applicantsCountryList)},
                new String[]{"申请人地址", getObject(patentOverview.get("address")), "预估到期日", getObject(patentOverview.get("estimated_maturity_date"))},
                new String[]{"当前权利人", String.join(",", patenteesList), "发明人（原始）", String.join(",", inventorsList)},
                new String[]{"权利要求数量", getObject(patentOverview.get("claims_num")), "文献页数", getObject(patentOverview.get("page"))}
        }).border(BorderStyle.DEFAULT).create();
        dataMap.put("basicInfo", basicInfoTable);

        // 专利摘要
        dataMap.put("abstract_cn", patentOverview.get("abstract_cn"));
        dataMap.put("abstract_en", patentOverview.get("abstract_en"));

        // 1.3 分类信息
        // 主ipc分类
        dataMap.put("main_ipc", getObject(patentOverview.get("main_ipc")));
        final Object mainIpcTreeObject = patentOverview.get("main_ipc_tree");
        if (mainIpcTreeObject instanceof PatentPortraitIndustryIpcBO) {
            final PatentPortraitIndustryIpcBO mainIpcTree = (PatentPortraitIndustryIpcBO) mainIpcTreeObject;
            dataMap.put("main_ipc_table", getTableRenderDataFromIpcTree(mainIpcTree));
        }

        // ipc分类号
        final Object ipcTreeObject = patentOverview.get("ipc_tree");
        if (ipcTreeObject instanceof List) {
            final List<String> ipcList = new ArrayList<>();
            final List<Map<String, Object>> ipcTableList = new ArrayList<>();
            ((List) ipcTreeObject).forEach(item -> {
                if (item instanceof PatentPortraitClassNumberTreeBO) {
                    final PatentPortraitClassNumberTreeBO classNumberTreeBO = (PatentPortraitClassNumberTreeBO) item;
                    ipcList.add(classNumberTreeBO.getClassNumber());
                    final PatentPortraitIndustryIpcBO tree = classNumberTreeBO.getTree();
                    ipcTableList.add(new HashMap<String, Object>() {{
                        put("ipc_table", getTableRenderDataFromIpcTree(tree));
                        put("ipc_content", "");
                    }});
                }
            });
            final String ipcListString = String.join(", ", ipcList);
            dataMap.put("ipcListString", ipcListString);
            dataMap.put("ipcList", ipcTableList);
        }

        // cpc分类号
        final Object cpcTreeObject = patentOverview.get("cpc_tree");
        if (cpcTreeObject instanceof List) {
            final List<String> cpcList = new ArrayList<>();
            final List<Map<String, Object>> cpcTableList = new ArrayList<>();
            ((List) cpcTreeObject).forEach(item -> {
                if (item instanceof PatentPortraitClassNumberTreeBO) {
                    final PatentPortraitClassNumberTreeBO classNumberTreeBO = (PatentPortraitClassNumberTreeBO) item;
                    cpcList.add(classNumberTreeBO.getClassNumber());
                    final PatentPortraitIndustryIpcBO tree = classNumberTreeBO.getTree();
                    cpcTableList.add(new HashMap<String, Object>() {{
                        put("cpc_table", getTableRenderDataFromIpcTree(tree));
                        put("cpc_content", "");
                    }});
                }
            });
            final String cpcListString = String.join(", ", cpcList);
            dataMap.put("cpcListString", cpcListString);
            dataMap.put("cpcList", cpcTableList);
        }

        // 行业分类
        final TableRenderData industryTable = Tables.of(new String[][]{
                new String[]{"国民经济行业分类", getIdNameString(patentOverview.get("nec_class_list"))},
                new String[]{"国民经济行业(主)", getIdNameString(patentOverview.get("nec_class_main_list"))},
                new String[]{"战新产业分类", getIdNameString(patentOverview.get("sec_class_list"))},
                new String[]{"战新产业(主)", getIdNameString(patentOverview.get("sec_class_main_list"))}
        }).border(BorderStyle.DEFAULT).create();
        dataMap.put("industry_table", industryTable);

        // 同族信息 instance_doc_patent-ec82453775b2e8cf8a20982a0fa63690这个专利有多种同族专利
        final List<List<String>> familyList = new ArrayList<>();
        final Object simpleFamilyCountry = patentOverview.get("simple_family_country");
        final AtomicInteger simpleIndex = new AtomicInteger(0);
        if (simpleFamilyCountry instanceof List) {
            ((List) simpleFamilyCountry).forEach(item -> {
                if (item instanceof PatentPortraitFamilyPatentBO) {
                    final PatentPortraitFamilyPatentBO familyPatentBO = (PatentPortraitFamilyPatentBO) item;
                    final List<String> list = new ArrayList<>();
                    if (simpleIndex.get() == 0) {
                        list.add("同族专利公开号");
                    } else {
                        list.add("");
                    }
                    list.add(familyPatentBO.getCountry());
                    list.add(String.join(",", familyPatentBO.getList()));
                    familyList.add(list);
                    simpleIndex.incrementAndGet();
                }
            });
        }
        final Object completeFamilyCountry = patentOverview.get("complete_family_country");
        final AtomicInteger completeIndex = new AtomicInteger(0);
        if (completeFamilyCountry instanceof List) {
            ((List) completeFamilyCountry).forEach(item -> {
                if (item instanceof PatentPortraitFamilyPatentBO) {
                    final PatentPortraitFamilyPatentBO familyPatentBO = (PatentPortraitFamilyPatentBO) item;
                    final List<String> list = new ArrayList<>();
                    if (completeIndex.get() == 0) {
                        list.add("扩展专利公开号");
                    } else {
                        list.add("");
                    }
                    list.add(familyPatentBO.getCountry());
                    list.add(String.join(",", familyPatentBO.getList()));
                    familyList.add(list);
                    completeIndex.incrementAndGet();
                }
            });
        }
        final String[][] familyTableArray = new String[familyList.size()][];
        for (int i = 0; i < familyList.size(); i++) {
            familyTableArray[i] = familyList.get(i).toArray(new String[0]);
        }
        final TableRenderData familyTable = Tables.of(familyTableArray)
                .border(BorderStyle.DEFAULT).create();
        // 合并单元格，把第0列的第0行到第simpleIndex.get() - 1行合并
        final MergeCellRule.MergeCellRuleBuilder builder = MergeCellRule.builder();
        if (simpleIndex.get() > 1) {
            builder.map(MergeCellRule.Grid.of(0, 0), MergeCellRule.Grid.of(simpleIndex.get() - 1, 0));
        }
        if (completeIndex.get() > 1) {
            builder.map(MergeCellRule.Grid.of(simpleIndex.get(), 0), MergeCellRule.Grid.of(simpleIndex.get() + completeIndex.get() - 1, 0));
        }
        final MergeCellRule rule = builder.build();
        familyTable.setMergeRule(rule);
        dataMap.put("familyTable", familyTable);

        // 代理信息
        final TableRenderData agentTable = Tables.of(new String[][]{
                new String[]{"代理机构", getObject(patentOverview.get("agency"))},
                new String[]{"代理人", getObject(patentOverview.get("agent"))}
        }).border(BorderStyle.DEFAULT).create();
        dataMap.put("agentTable", agentTable);

        // 首项权利要求
        dataMap.put("first_claim", getObject(patentOverview.get("first_claim")));

        // 2 技术分析
        // 解决问题
        dataMap.put("problem", getObject(patentOverview.get("problem")));

        // 关键技术
        final Object technology = patentOverview.get("technology");
        if (technology instanceof List) {
            final List<Map<String, Object>> tableList = new ArrayList<>();
            ((List) technology).forEach(item -> {
                if (item instanceof Map) {
                    final Map itemMap = (Map) item;
                    tableList.add(new HashMap<String, Object>() {{
                        put("technologyName", getObject(itemMap.get("name")));
                        put("technologyDesc", getObject(itemMap.get("desc")));
                    }});
                }
            });
            dataMap.put("technologyList", tableList);
        }

        // 技术效果
        dataMap.put("effect", getObject(patentOverview.get("effect")));
        return patentName;
    }

    private String getIdNameString(final Object object) {
        final List<String> necClassStringList = new ArrayList<>();
        if (object instanceof List) {
            ((List) object).forEach(item -> {
                if (item instanceof Map) {
                    necClassStringList.add(((Map) item).get("id") + " "
                            + ((Map) item).get("name"));
                } else if (item instanceof PatentPortraitIndustryClassBO) {
                    necClassStringList.add(((PatentPortraitIndustryClassBO) item).getId() + " "
                            + ((PatentPortraitIndustryClassBO) item).getName());
                }
            });
        }
        return String.join(";", necClassStringList);
    }

    private TableRenderData getTableRenderDataFromIpcTree(PatentPortraitIndustryIpcBO ipcBO) {
        final List<List<String>> mainIpcTableList = new ArrayList<>();
        while (CollUtil.isNotEmpty(ipcBO.getChildren())) {
            final List<PatentPortraitIndustryIpcBO> children = ipcBO.getChildren();
            ipcBO = children.get(0);
            final List<String> row = new ArrayList<>(2);
            row.add(ipcBO.getCode());
            row.add(ipcBO.getName());
            mainIpcTableList.add(row);
        }
        final String[][] mainIpcTableArray = new String[mainIpcTableList.size()][];
        for (int i = 0; i < mainIpcTableList.size(); i++) {
            mainIpcTableArray[i] = mainIpcTableList.get(i).toArray(new String[0]);
        }
        return Tables.of(mainIpcTableArray)
                .border(BorderStyle.DEFAULT).create();
    }

    /**
     * 设置价值评价内容
     *
     * @param id
     * @param dataMap
     */
    public void setValueEvaluation(final String id, final Map<String, Object> dataMap) throws IOException {
        //总体评价
        final PatentValueOverallEvaluationVO evaluationVO = patentDimensionService.evaluationResult(id);
        dataMap.put("overall_evaluation", evaluationVO.getSummarize()
                .replaceAll("<span style=\"color:#156fff;\">", "")
                .replaceAll("</span>", ""));
        //整体评价结果
        final StringBuilder wholeEvaluation = new StringBuilder();
        TableRenderData wholeEvaluationTable = new TableRenderData();
        if (evaluationVO.getResultBO() != null) {
            wholeEvaluation.append("经综合评价，该专利价值评价在IPC分类")
                    .append(evaluationVO.getResultBO().getIpcLevel3Code())
                    .append("技术集群中超过")
                    .append(evaluationVO.getResultBO().getRatio())
                    .append("%")
                    .append("的其他专利，综合价值表现")
                    .append(evaluationVO.getResultBO().getEvaluationResult());

            wholeEvaluationTable = Tables.of(new String[][]{
                    new String[]{"评价结果", getObject(evaluationVO.getResultBO().getEvaluationResult()),
                            "IPC分类排名", getObject(evaluationVO.getResultBO().getIpcLevel3Code()
                            + "技术集群中超过" + evaluationVO.getResultBO().getRatio() + "%的专利")}
            }).border(BorderStyle.DEFAULT).create();
        }
        dataMap.put("whole_evaluation", wholeEvaluation);
        dataMap.put("whole_evaluation_table", wholeEvaluationTable);
        //评价结果对比
        final PatentValueOverallEvaluationResultCompareVO compareVO = patentDimensionService.evaluationResultCompare(id);
        final StringBuilder evaluationCompare = new StringBuilder();
        evaluationCompare.append("相较专利所处技术领域集群中位数价值表现，")
                .append(compareVO.getBest())
                .append("维度表现最好，")
                .append(compareVO.getWorst())
                .append("维度表现最差，合计")
                .append(compareVO.getCount())
                .append("项维度低于集群中位数。");
        dataMap.put("evaluation_compare", evaluationCompare);
        final RowRenderData evaluationCompareTableRow0 = Rows.of("细分维度", "本专利分值", evaluationVO.getResultBO().getIpcLevel3Code() + "技术集群中位数分值", "差值")
                .textColor("FFFFFF")
                .bgColor("4472C4").center().create();
        final Map<String, String> map = new HashMap<>();
        for (final CommonDataForCountBO bo : compareVO.getPatentMedianValueList()) {
            map.put(bo.getName(), bo.getData());
        }
        final List<RowRenderData> evaluationCompareTableDataList = new ArrayList<>();
        evaluationCompareTableDataList.add(evaluationCompareTableRow0);
        compareVO.getPatentValueList().forEach(item -> {
            evaluationCompareTableDataList.add(Rows.of(
                    getObject(item.getName()),
                    getObject(item.getData()),
                    getObject(map.get(item.getName())),
                    getObject((!StringUtils.isEmpty(item.getData()) && !StringUtils.isEmpty(map.get(item.getName()))) ?
                            Double.parseDouble(item.getData()) - Double.parseDouble(map.get(item.getName())) : "-")
            ).create());
        });
        dataMap.put("evaluation_compare_table", Tables.create(evaluationCompareTableDataList.toArray(new RowRenderData[0])));
        //预估市场价值
        final PatentValueOverallEvaluationMarketValueVO marketValueVO = patentDimensionService.evaluationMarketValue(id);
        final StringBuilder estimatedEvaluation = new StringBuilder();
        estimatedEvaluation.append("经测算，该专利预估市场价值为")
                .append(marketValueVO.getMarketValue())
                .append("万美元，")
                .append("在IPC分类")
                .append(marketValueVO.getIpcLevel3Code())
                .append("技术集群中超过")
                .append(marketValueVO.getIpcLevel3CodeRatio())
                .append("%的其他专利，")
                .append("在IPC分类")
                .append(marketValueVO.getMainIpc())
                .append("细分技术中超过")
                .append(marketValueVO.getMainIpcRatio())
                .append("%的其他专利");
        dataMap.put("estimated_evaluation", estimatedEvaluation);
        final TableRenderData estimatedEvaluationTable = Tables.of(new String[][]{
                new String[]{"预估专利市场价值：", getObject(marketValueVO.getMarketValue() + "万美元"),
                        "较" + marketValueVO.getIpcLevel3Code() + "技术集群平均价值：",
                        getObject((marketValueVO.getCompareAvgValue().contains("-") ? "低" : "高")
                                + marketValueVO.getCompareAvgValue().replace("-", "") + "%"),
                        marketValueVO.getIpcLevel3Code() + "技术集群价值排名：",
                        getObject("超过" + marketValueVO.getIpcLevel3CodeRatio() + "%的专利"),
                        marketValueVO.getMainIpc() + "技术分类价值排名：", getObject("超过" + marketValueVO.getMainIpcRatio() + "%的专利")}
        }).border(BorderStyle.DEFAULT).create();
        dataMap.put("estimated_evaluation_table", estimatedEvaluationTable);
        //维度评价
        final PatentValueDimensionEvaluationVO dimensionEvaluationVO = patentDimensionService.evaluationDimension(id, "industry_vehicle");
        final StringBuilder dimensionEvaluation = new StringBuilder();
        dimensionEvaluation.append("通过技术价值、通用价值、法律价值、战略价值、市场价值等五个维度评价该专利，其中")
                .append(dimensionEvaluationVO.getBest())
                .append("在")
                .append(dimensionEvaluationVO.getIpcLevel3Code())
                .append("技术集群中表现最好，")
                .append(dimensionEvaluationVO.getWorst())
                .append("在")
                .append(dimensionEvaluationVO.getIpcLevel3Code())
                .append("技术集群中表现最差，共发现该专利")
                .append(dimensionEvaluationVO.getCount())
                .append("个亮点指标。");
        dataMap.put("dimension_evaluation", dimensionEvaluation);
        if (!CollectionUtils.isEmpty(dimensionEvaluationVO.getList())) {
            final RowRenderData dimensionEvaluationTableRow0 = Rows.of("细分维度", "评价结果", "IPC分类排名", "价值亮点")
                    .textColor("FFFFFF")
                    .bgColor("4472C4").center().create();
            final Map<String, String> ratioMap = new HashMap<>();
            ratioMap.put("入门", "(超过0%-20%)");
            ratioMap.put("普通", "(超过20%-35%)");
            ratioMap.put("良好", "(超过35%-50%)");
            ratioMap.put("优秀", "(超过50%-70%)");
            ratioMap.put("卓越", "(超过70%-100%)");
            final List<RowRenderData> dimensionEvaluationTableDataList = new ArrayList<>();
            dimensionEvaluationTableDataList.add(dimensionEvaluationTableRow0);
            dimensionEvaluationVO.getList().forEach(item -> {
                dimensionEvaluationTableDataList.add(Rows.of(
                        getObject(item.getName()),
                        getObject(item.getEvaluationResult() + ratioMap.get(item.getEvaluationResult())),
                        getObject(item.getIpcLevel3Code() + "超过" + item.getRatio() + "%的专利"),
                        getObject(item.getValueDesc())
                ).create());
            });
            dataMap.put("dimension_evaluation_table", Tables.create(dimensionEvaluationTableDataList.toArray(new RowRenderData[0])));
        }

    }

    /**
     * 设置同族专利内容
     *
     * @param id
     * @param dataMap
     */
    public void setCognatePatent(final String id, final Map<String, Object> dataMap) throws IOException {
        final List<Map<String, Object>> simplePatentList = patentPortraitService.cognateList(id, 2, null);
        dataMap.put("simpleFamilyNum", simplePatentList.size());
        if (CollUtil.isNotEmpty(simplePatentList)) {
            final RowRenderData row0 = Rows.of("序号", "公开(公告)号", "标题(中文)", "优先权号", "公开(公告日)", "申请号", "申请日", "国别或地区", "同族类别")
                    .textColor("FFFFFF")
                    .bgColor("4472C4").center().create();
            final List<RowRenderData> rowDataList = new ArrayList<>();
            rowDataList.add(row0);
            final AtomicInteger index = new AtomicInteger(0);
            simplePatentList.forEach(item -> {
                rowDataList.add(Rows.of(String.valueOf(index.incrementAndGet()),
                        getObject(item.get("public_code")),
                        getObject(item.get("title_cn")),
                        getObject(item.get("priority_code")),
                        getObject(item.get("public_date")),
                        getObject(item.get("apply_code")),
                        getObject(item.get("apply_date")),
                        getObject(item.get("public_country")),
                        getObject(item.get("family_type"))
                ).create());
            });
            dataMap.put("simpleFamilyTable", Tables.create(rowDataList.toArray(new RowRenderData[0])));
        }

        final List<Map<String, Object>> expandPatentList = patentPortraitService.cognateList(id, 3, null);
        dataMap.put("expandFamilyNum", expandPatentList.size());
        if (CollUtil.isNotEmpty(expandPatentList)) {
            final RowRenderData row0 = Rows.of("序号", "公开(公告)号", "标题(中文)", "优先权号", "公开(公告日)", "申请号", "申请日", "国别或地区", "同族类别")
                    .textColor("FFFFFF")
                    .bgColor("4472C4").center().create();
            final List<RowRenderData> rowDataList = new ArrayList<>();
            rowDataList.add(row0);
            final AtomicInteger index = new AtomicInteger(0);
            expandPatentList.forEach(item -> {
                rowDataList.add(Rows.of(String.valueOf(index.incrementAndGet()),
                        getObject(item.get("public_code")),
                        getObject(item.get("title_cn")),
                        getObject(item.get("priority_code")),
                        getObject(item.get("public_date")),
                        getObject(item.get("apply_code")),
                        getObject(item.get("apply_date")),
                        getObject(item.get("public_country")),
                        getObject(item.get("family_type"))
                ).create());
            });
            dataMap.put("expandFamilyTable", Tables.create(rowDataList.toArray(new RowRenderData[0])));
        }

    }

    /**
     * 设置引证专利内容
     *
     * @param id
     * @param dataMap
     */
    public void setCitedPatent(final String id, final Map<String, Object> dataMap) throws IOException {
        final Map<String, Object> quoteList = patentPortraitService.quoteChartOrList(id, 2);
        final Object ctList = quoteList.get("ct_list");
        final Object ctfwList = quoteList.get("ctfw_list");
        setCitedOrCitingPatent(ctList, dataMap, "citingPatentNum", "citingPatenTable");
        setCitedOrCitingPatent(ctfwList, dataMap, "citedPatentNum", "citedPatentTable");
    }

    private void setCitedOrCitingPatent(final Object ctList, final Map<String, Object> dataMap,
                                        final String numField, final String tableField) {
        if (ctList instanceof List) {
            final List<Map<String, Object>> ctMapList = (List<Map<String, Object>>) ctList;
            dataMap.put(numField, ctMapList.size());
            if (CollUtil.isNotEmpty(ctMapList)) {
                final RowRenderData row0 = Rows.of("序号", "公开(公告)号", "标题", "申请人", "公开(公告日)", "申请号", "申请日", "IPC分类号")
                        .textColor("FFFFFF")
                        .bgColor("4472C4").center().create();
                final List<RowRenderData> rowDataList = new ArrayList<>();
                rowDataList.add(row0);
                final AtomicInteger index = new AtomicInteger(0);
                ctMapList.forEach(item -> {
                    rowDataList.add(Rows.of(String.valueOf(index.incrementAndGet()),
                            getObject(item.get("public_code")),
                            getObject(item.get("title")),
                            getObject(item.get("applicants")),
                            getObject(item.get("public_date")),
                            getObject(item.get("apply_code")),
                            getObject(item.get("apply_date")),
                            getObject(item.get("ipc"))
                    ).create());
                });
                dataMap.put(tableField, Tables.create(rowDataList.toArray(new RowRenderData[0])));
            }
        }
    }

    /**
     * 设置相似专利内容
     *
     * @param id
     * @param dataMap
     */
    public void setSimilarPatent(final String id, final Map<String, Object> dataMap) {
        final EsPageResult esPageResult = patentEsInfoService.querySimilarPatent(id, 1, 10);
        final List<Map<String, Object>> similarPatentList = esPageResult.getList();
        if (CollUtil.isNotEmpty(similarPatentList)) {
            final RowRenderData row0 = Rows.of("序号", "公开(公告)号", "标题(中文)", "申请人", "公开(公告日)", "申请号", "申请日", "同族类别")
                    .textColor("FFFFFF")
                    .bgColor("4472C4").center().create();
            final List<RowRenderData> rowDataList = new ArrayList<>();
            rowDataList.add(row0);
            final AtomicInteger index = new AtomicInteger(0);
            similarPatentList.forEach(item -> {
                rowDataList.add(Rows.of(String.valueOf(index.incrementAndGet()),
                        getObject(item.get("public_code")),
                        getObject(item.get("title")),
                        getObject(item.get("applicants")),
                        getObject(item.get("public_date")),
                        getObject(item.get("apply_code")),
                        getObject(item.get("apply_date")),
                        getObject(item.get("homoclan_category"))
                ).create());
            });
            dataMap.put("similarPatentTable", Tables.create(rowDataList.toArray(new RowRenderData[0])));
        }
    }

    private String getObject(final Object object) {
        if (object instanceof String) {
            return object.toString();
        } else if (object instanceof List) {
            final List<String> itemList = new ArrayList<>();
            ((List) object).forEach(item -> {
                if (item instanceof String) {
                    itemList.add((String) item);
                } else if (item instanceof Map) {
                    itemList.add((String) ((Map) item).get("name"));
                }
            });
            return String.join(";", itemList);
        } else if (object != null) {
            return String.valueOf(object);
        }
        return "";
    }

    /**
     * 使用docx4j将word转成pdf
     *
     * @param wordPath
     * @param targetPdfPath
     */
    public void docxToPdf(final String wordPath, final String targetPdfPath) {
        try {
            final TransformerFactory transformerFactory = TransformerFactory.newInstance();
            transformerFactory.setAttribute(XMLConstants.ACCESS_EXTERNAL_STYLESHEET, "all");
        } catch (final Exception e) {
            log.error("docxToPdf error", e);
        }
        try {
            final WordprocessingMLPackage pkg = Docx4J.load(new File(wordPath));
            final Mapper fontMapper = new IdentityPlusMapper();
            fontMapper.put("宋体", PhysicalFonts.get("SimSun"));
            pkg.setFontMapper(fontMapper);
            Docx4J.toPDF(pkg, new FileOutputStream(targetPdfPath));
        } catch (final Exception e) {
            // 忽略不支持的属性设置
            log.error("word转pdf失败", e);
        }
    }
}
