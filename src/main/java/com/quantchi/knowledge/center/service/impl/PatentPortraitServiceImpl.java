package com.quantchi.knowledge.center.service.impl;

import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.quantchi.knowledge.center.bean.bo.*;
import com.quantchi.knowledge.center.bean.enums.EsIndexEnum;
import com.quantchi.knowledge.center.config.ModelParamConfig;
import com.quantchi.knowledge.center.dao.mysql.IndustryIpcMapper;
import com.quantchi.knowledge.center.helper.ElasticsearchHelper;
import com.quantchi.knowledge.center.service.PatentPortraitService;
import com.quantchi.knowledge.center.util.RedisUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.aggregations.AggregationBuilders;
import org.elasticsearch.search.aggregations.bucket.histogram.DateHistogramAggregationBuilder;
import org.elasticsearch.search.aggregations.bucket.histogram.DateHistogramInterval;
import org.elasticsearch.search.aggregations.bucket.histogram.Histogram;
import org.elasticsearch.search.aggregations.bucket.histogram.ParsedDateHistogram;
import org.elasticsearch.search.aggregations.bucket.terms.ParsedTerms;
import org.elasticsearch.search.aggregations.bucket.terms.Terms;
import org.elasticsearch.search.aggregations.bucket.terms.TermsAggregationBuilder;
import org.elasticsearch.search.aggregations.metrics.ParsedTopHits;
import org.elasticsearch.search.aggregations.metrics.TopHitsAggregationBuilder;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.io.IOException;
import java.time.Duration;
import java.util.*;

@Service
@Slf4j
public class PatentPortraitServiceImpl implements PatentPortraitService {

    @Autowired
    private ElasticsearchHelper elasticsearchHelper;

    @Autowired
    private IndustryIpcMapper industryIpcMapper;

    @Autowired
    private LibraryInfoService libraryInfoService;

    @Autowired
    private ModelParamConfig modelParamConfig;

    // 自定义自己的业务id
    private static final String requestIdTemplate = "liangzhi-%d";

    @Qualifier("docExecutor")
    @Autowired
    private ThreadPoolTaskExecutor docExecutor;

    private static final String MODEL_URL = "https://open.bigmodel.cn/api/paas/v4/chat/completions";

    /**
     * 智谱同步方法调用,返回解析后的json数据
     *
     * @return 解析后的JSON数据
     */
    public String zhipuSyncCall(final String prompt, final String beginStr, final String endStr) {
        try {
            // 构建消息列表
            final List<Map<String, Object>> messages = new ArrayList<>();
            final Map<String, Object> message = new HashMap<String, Object>();
            message.put("role", "user");
            message.put("content", prompt);
            messages.add(message);
            
            // 构建请求体
            final Map<String, Object> requestBody = new HashMap<>();
            requestBody.put("model", "glm-4-flash-250414");
            requestBody.put("messages", messages);
            requestBody.put("stream", Boolean.FALSE);
            
            // 生成请求ID
            final String requestId = String.format(requestIdTemplate, System.currentTimeMillis());
            requestBody.put("request_id", requestId);
            
            // 发送HTTP请求
            final HttpRequest httpRequest = HttpRequest.post(modelParamConfig.getOneApiUrl())
                    .header("Content-Type", "application/json")
                    .header("Authorization", "Bearer " + modelParamConfig.getDpAccessToken())
                    .body(JSON.toJSONString(requestBody));
            
            final HttpResponse response = httpRequest.execute();
            if (response.getStatus() != 200) {
                log.error("智谱API调用失败，状态码: {}", response.getStatus());
                return null;
            }
            
            final String responseBody = response.body();
            final JSONObject jsonResponse = JSON.parseObject(responseBody);
            
            // 解析返回结果
            if (!jsonResponse.containsKey("choices") || jsonResponse.getJSONArray("choices").isEmpty()) {
                log.error("智谱API返回结果格式错误，无法找到choices字段");
                return null;
            }
            
            final JSONObject choice = jsonResponse.getJSONArray("choices").getJSONObject(0);
            final JSONObject responseMessage = choice.getJSONObject("message");
            
            if (responseMessage == null || !responseMessage.containsKey("content")) {
                log.error("智谱API返回结果格式错误，无法找到content字段");
                return null;
            }
            
            final String content = responseMessage.getString("content");
            log.info("这次调用输入的字符数是{},输出的字符数是{}, 输入{}, 输出{}", prompt.length(), content.length(), prompt, content);
            
            // 提取JSON数据部分
            final int startIndex = content.indexOf(beginStr);     // 找到开始标记的位置
            final int endIndex = content.lastIndexOf(endStr) + 1; // 找到结束标记的位置
            
            if (startIndex == -1 || endIndex == 0) {
                log.error("无法在字符串中找到JSON数据");
                return null;
            } else {
                return content.substring(startIndex, endIndex);
            }
        } catch (Exception e) {
            log.error("调用智谱API异常", e);
            return null;
        }
    }

    @Override
    public Map<String, Object> patentOverview(final String id) throws IOException {
        final String[] includes = {"id", "title_cn", "title_en", "title", "patent_type", "status", "apply_code", "apply_date",
                "public_code", "public_date", "estimated_maturity_date", "applicants", "applicants_country",
                "address", "inventors", "claims_num", "page", "abstract", "abstract_cn", "abstract_en",
                "main_ipc", "ipc", "cpc", "nec", "sec", "simple_family",
                "complete_family", "agency", "agent", "first_claim", "pdf_url", "problem", "effect", "technology", "patentees"};//查询字段
        final Map<String, Object> source = elasticsearchHelper.getDataById(EsIndexEnum.PATENT.getEsIndex(), id, includes, new String[]{});
        // 优先取title_cn，然后是title_en，最后是title
        source.put("title", source.getOrDefault("title_cn", source.getOrDefault("title_en", source.get("title"))));
        // 优先取abstract_cn，然后是abstract_en，最后是abstract
        source.put("abstract", source.getOrDefault("abstract_cn", source.getOrDefault("abstract_en", source.get("abstract"))));
        getPatentAnalysisFromModelOrCache(source);
        // 替换首项权利要求中的换行符
        final Object firstClaim = source.get("first_claim");
        if (firstClaim instanceof String) {
            String text = (String) firstClaim;
            text = text.replace("/n", "\n");
            source.put("first_claim", text);
        }

        // 查询申请人和专利权人对应的公司id是否存在
        libraryInfoService.dealWthCompanyId(source.get("patentees"));
        libraryInfoService.dealWthCompanyId(source.get("applicants"));

        //主分类号 树
        final String mainIpc = (String) source.get("main_ipc");
        source.put("main_ipc_tree", getClassNumberTree(mainIpc));

        //ipc分类号树
        final List<String> ipc = (List<String>) source.get("ipc");
        final List<PatentPortraitClassNumberTreeBO> ipcTree = new ArrayList<>();
        if (!CollectionUtils.isEmpty(ipc)) {
            for (final String item : ipc) {
                final PatentPortraitClassNumberTreeBO treeBO = new PatentPortraitClassNumberTreeBO();
                treeBO.setClassNumber(item);
                treeBO.setTree(getClassNumberTree(item));
                ipcTree.add(treeBO);
            }
        }
        source.put("ipc_tree", ipcTree);

        //cpc分类号树
        final List<String> cpc = (List<String>) source.get("cpc");
        final List<PatentPortraitClassNumberTreeBO> cpcTree = new ArrayList<>();
        if (!CollectionUtils.isEmpty(cpc)) {
            for (final String item : cpc) {
                final PatentPortraitClassNumberTreeBO treeBO = new PatentPortraitClassNumberTreeBO();
                treeBO.setClassNumber(item);
                treeBO.setTree(getClassNumberTree(item));
                cpcTree.add(treeBO);
            }
        }
        source.put("cpc_tree", cpcTree);

        //国民经济行业分类
        final List<String> necList = (List<String>) source.get("nec");
        if (!CollectionUtils.isEmpty(necList)) {
            final List<PatentPortraitIndustryClassBO> necClassList = industryIpcMapper.getNameByNec(necList);
            source.put("nec_class_list", necClassList);
            //国民经济行业（主）
            final List<PatentPortraitIndustryClassBO> necClassMainList = new ArrayList<>();
            if (!CollectionUtils.isEmpty(necClassList)) {
                necClassMainList.add(necClassList.get(0));
            }
            source.put("nec_class_main_list", necClassMainList);
        } else {
            source.put("nec_class_list", null);
            source.put("nec_class_main_list", null);
        }

        //战新产业分类
        final List<String> secList = (List<String>) source.get("sec");
        if (!CollectionUtils.isEmpty(secList)) {
            final List<PatentPortraitIndustryClassBO> secClassList = industryIpcMapper.getNameBySec(secList);
            source.put("sec_class_list", secClassList);
            //战新产业（主）
            final List<PatentPortraitIndustryClassBO> secClassMainList = new ArrayList<>();
            if (!CollectionUtils.isEmpty(secClassList)) {
                secClassMainList.add(secClassList.get(0));
            }

            source.put("sec_class_main_list", secClassMainList);
        } else {
            source.put("sec_class_list", null);
            source.put("sec_class_main_list", null);
        }

        //同族专利公开号
        final List<String> simpleFamily = (List<String>) source.get("simple_family");
        source.put("simple_family_country", getFamilyPatentForCountry(simpleFamily));

        //拓展同族专利公开号
        final List<String> completeFamily = (List<String>) source.get("complete_family");
        source.put("complete_family_country", getFamilyPatentForCountry(completeFamily));

        return source;
    }



    /**
     * 使用大模型生成"problem", "effect", "technology",已生成的就直接从缓存中读取
     * @param source
     */
    private void getPatentAnalysisFromModelOrCache(final Map<String, Object> source) {
        final String id = (String) source.get("id");
        final Object problem = source.get("problem");
        final Object effect = source.get("effect");
        final Object technology = source.get("technology");
        final String cacheKey = "patentAnalysis:" + id;
        if (problem != null && effect != null && technology != null) {
            return;
        }
        if (checkCache(source, id, cacheKey)) {
            return;
        }
        final String LOCK_PREFIX = "patent:analysis:lock:";
        final Boolean isLocked = RedisUtils.tryLock(LOCK_PREFIX + id, 10, 10);
        if (!isLocked) {
            log.warn("获取专利分析锁失败，专利ID: {}", id);
            return;
        }
        // 再次检查缓存
        if (checkCache(source, id, cacheKey)) {
            return;
        }
        docExecutor.execute(() -> {
            final String prompt = "你是一位资深的专利分析师，你的任务是为用户在产业专利技术领域提供专业、准确、有见地的建议。\n" +
                    "根据下面的专利内容，分析这篇专利主要解决了什么问题，采用了什么关键技术，以及达到的效果怎么样。\n" +
                    "要求：以json格式输出解决的问题(problem)、采用的技术方向(technology)、达到的效果(effect)。其中problem和effect只需要直接输出文本，无需增加key和value，technology需要给出一个列表，列表里是多个对象，对象的key分别是name和desc\n" +
                    "专利标题：【" + source.get("title") + "】\n" +
                    "摘要：【" + source.get("abstract") + "】\n" +
                    "首项权利要求：【" + source.get("first_claim") + "】";
            final String jsonData = zhipuSyncCall(prompt, "{", "}");
            final JSONObject jsonObject = JSONObject.parseObject(jsonData);
            if (jsonObject == null) {
                log.error("zhipuSyncCall error:{}", jsonData);
                return;
            }
            source.put("problem", jsonObject.get("problem"));
            source.put("effect", jsonObject.get("effect"));
            source.put("technology", jsonObject.get("technology"));
            RedisUtils.setCacheObject(cacheKey + ":problem", jsonObject.get("problem"), Duration.ofDays(30L));
            RedisUtils.setCacheObject(cacheKey + ":effect", jsonObject.get("effect"), Duration.ofDays(30L));
            RedisUtils.setCacheObject(cacheKey + ":technology", jsonObject.get("technology"), Duration.ofDays(30L));
        });
    }

    private boolean checkCache(final Map<String, Object> source, final String id, final String cacheKey) {
        final Object problemCache = RedisUtils.getCacheObject(cacheKey + ":problem");
        final Object effectCache = RedisUtils.getCacheObject(cacheKey + ":effect");
        final Object technologyCache = RedisUtils.getCacheObject(cacheKey + ":technology");
        if (problemCache != null && effectCache != null && technologyCache != null) {
            source.put("problem", problemCache);
            source.put("effect", effectCache);
            source.put("technology", technologyCache);
            return true;
        }
        return false;
    }

    /**
     * 获取分类号树
     *
     * @return
     */
    private PatentPortraitIndustryIpcBO getClassNumberTree(final String classNumber) {
        PatentPortraitIndustryIpcBO tree = new PatentPortraitIndustryIpcBO();
        final PatentPortraitIndustryIpcBO ipcBO = industryIpcMapper.getIpcByCode(classNumber);
        if (ipcBO != null) {
            //分类号必须在库里
            final List<PatentPortraitIndustryIpcBO> treeNode = new ArrayList<>();
            treeNode.add(ipcBO);
            String parentId = ipcBO.getParentId();
            while (!StringUtils.isEmpty(parentId)) {
                final PatentPortraitIndustryIpcBO mainIpcParentBO = industryIpcMapper.getIpcByParentId(parentId);
                treeNode.add(mainIpcParentBO);
                parentId = mainIpcParentBO.getParentId();
            }
            // 处理ipcTreeNode 组树
            final Map<String, PatentPortraitIndustryIpcBO> treeNodeMap = new HashMap<>();
            for (final PatentPortraitIndustryIpcBO bo : treeNode) {
                treeNodeMap.put(bo.getId(), bo);
            }
            for (final PatentPortraitIndustryIpcBO bo : treeNode) {
                if (StringUtils.isEmpty(bo.getParentId())) {
                    tree = bo;
                } else {
                    final PatentPortraitIndustryIpcBO parent = treeNodeMap.get(bo.getParentId());
                    parent.getChildren().add(bo);
                }
            }
        }

        return tree;
    }

//    private List<PatentPortraitFamilyPatentBO> getFamilyPatentForCountry(List<String> familyPatent) {
//        List<PatentPortraitFamilyPatentBO> familyPatentList = new ArrayList<>();
//        if (!CollectionUtils.isEmpty(familyPatent)) {
//            //收集所有国别类型
//            List<String> countryEncoding = new ArrayList<>();
//            for (String item : familyPatent) {
//                String subString = item.substring(0, 2);
//                if (!countryEncoding.contains(subString)) {
//                    countryEncoding.add(subString);
//                }
//            }
//            List<PatentPortraitCountryEncodingBO> countryEncodingList = countryEncodingMapper.getCountryByEncoding(countryEncoding);
//            //根据查询到的国别个数创建数据集
//            for (PatentPortraitCountryEncodingBO bo : countryEncodingList) {
//                PatentPortraitFamilyPatentBO familyBO = new PatentPortraitFamilyPatentBO();
//                familyBO.setCountry(bo.getCountry());
//                familyBO.setEncoding(bo.getEncoding());
//                familyPatentList.add(familyBO);
//            }
//            Set<String> set = new HashSet<>();//放入数据集的专利
//            //将专利分类放入数据集
//            for (String item : familyPatent) {
//                for (PatentPortraitFamilyPatentBO bo : familyPatentList) {
//                    if (item.substring(0, 2).equals(bo.getEncoding())) {
//                        bo.getList().add(item);
//                        set.add(item);
//                    }
//                }
//            }
//            if (set.size() < familyPatent.size()) {
//                //说明存在找不到国别的专利 找出来放在其他分类
//                PatentPortraitFamilyPatentBO otherBO = new PatentPortraitFamilyPatentBO();
//                otherBO.setCountry("其他");
//                List<String> otherList = new ArrayList<>();
//                for (String item : familyPatent) {
//                    if (!set.contains(item)) {
//                        otherList.add(item);
//                    }
//                }
//                otherBO.setList(otherList);
//                familyPatentList.add(otherBO);
//            }
//        }
//
//        return familyPatentList;
//    }

    private List<PatentPortraitFamilyPatentBO> getFamilyPatentForCountry(final List<String> familyPatent) throws IOException {
        final List<PatentPortraitFamilyPatentBO> familyPatentList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(familyPatent)) {
            final SearchRequest searchRequest = new SearchRequest(EsIndexEnum.PATENT.getEsIndex());
            final SearchSourceBuilder searchBuilder = new SearchSourceBuilder();
            searchBuilder.fetchSource(new String[]{"id", "public_code"}, null);
            // 初始化布尔查询
            final BoolQueryBuilder filterBuilder = QueryBuilders.boolQuery();
            filterBuilder.filter(QueryBuilders.termsQuery("public_code", familyPatent));
            searchBuilder.query(filterBuilder);
            // 创建聚合构建器
            final TermsAggregationBuilder termsAggregationBuilder = AggregationBuilders
                    .terms("by_public_country") // 聚合名称
                    .field("public_country"); // 替换为你要聚合的字段名
            // 创建top_hits聚合构建器
            final TopHitsAggregationBuilder topHitsAggregationBuilder = AggregationBuilders
                    .topHits("top_hits")
                    .size(100); // 指定每个桶返回的顶部文档数量
            // 将top_hits聚合构建器添加到terms聚合构建器
            termsAggregationBuilder.subAggregation(topHitsAggregationBuilder);
            // 将terms聚合构建器添加到搜索源构建器
            searchBuilder.aggregation(termsAggregationBuilder);
            searchRequest.source(searchBuilder);
            final SearchResponse searchResponse = elasticsearchHelper.getRestHighLevelClient().search(searchRequest, RequestOptions.DEFAULT);

            // 解析terms聚合结果
            final ParsedTerms terms = searchResponse.getAggregations().get("by_public_country");
            // 遍历terms聚合桶
            for (final Terms.Bucket bucket : terms.getBuckets()) {
                final String key = bucket.getKeyAsString();
                final long docCount = bucket.getDocCount();
                final PatentPortraitFamilyPatentBO bo = new PatentPortraitFamilyPatentBO();
                bo.setCountry(key);
                bo.setNum((int) docCount);
                // 获取top_hits聚合结果
                final ParsedTopHits topHits = bucket.getAggregations().get("top_hits");
                for (final SearchHit hit : topHits.getHits().getHits()) {
                    bo.getList().add(hit.getSourceAsMap().get("public_code").toString());
                }
                familyPatentList.add(bo);
            }
        }
        return familyPatentList;
    }


    @Override
    public Map<String, Object> quoteChartOrList(final String id, final Integer type) throws IOException {
        final String[] includes = {"id", "public_code", "ct", "ctfw", "ct_times", "ctfw_times", "title_cn", "applicants",
                "public_date", "apply_code", "simple_family_num", "main_ipc"};//查询字段
        final Map<String, Object> source = elasticsearchHelper.getDataById(EsIndexEnum.PATENT.getEsIndex(), id, includes, new String[]{});
        final List<String> ctList = (List<String>) source.get("ct");//引证专利
        final List<String> ctfwList = (List<String>) source.get("ctfw");//被引证专利
        if (type == 1) {
            //引证分析图
            source.put("ct_children", getCtOrCtfwDetail(type, 1, ctList));
            source.put("ctfw_children", getCtOrCtfwDetail(type, 2, ctfwList));

            return source;
        } else {
            //引证列表
            final Map<String, Object> result = new HashMap<>();
            result.put("ct_list", getCtOrCtfwDetail(type, 1, ctList));
            result.put("ctfw_list", getCtOrCtfwDetail(type, 2, ctfwList));

            return result;
        }

    }

    private List<Map<String, Object>> getCtOrCtfwDetail(final Integer type, final Integer searchType, final List<String> list) throws IOException {
        final List<Map<String, Object>> children = new ArrayList<>();
        if (!CollectionUtils.isEmpty(list)) {
            final SearchRequest searchRequest = new SearchRequest(EsIndexEnum.PATENT.getEsIndex());
            final SearchSourceBuilder searchBuilder = new SearchSourceBuilder();
            if (type == 1) {
                //引证分析图
                if (searchType == 1) {
                    //引证专利
                    searchBuilder.fetchSource(new String[]{"id", "public_code", "ct_times", "title_cn", "applicants",
                            "public_date", "apply_code", "simple_family_num", "main_ipc"}, null);
                } else if (searchType == 2) {
                    //被引证专利
                    searchBuilder.fetchSource(new String[]{"id", "public_code", "ctfw_times", "title_cn", "applicants",
                            "public_date", "apply_code", "simple_family_num", "main_ipc"}, null);
                }
            } else if (type == 2) {
                //引证列表
                searchBuilder.fetchSource(new String[]{"id", "public_code", "title", "title_cn", "applicants", "public_date",
                        "apply_code", "apply_date", "ipc"}, null);
            }
            // 初始化布尔查询
            final BoolQueryBuilder filterBuilder = QueryBuilders.boolQuery();
            filterBuilder.filter(QueryBuilders.termsQuery("public_code", list));
            searchBuilder.size(10000);
            searchBuilder.query(filterBuilder);
            searchRequest.source(searchBuilder);
            final SearchResponse searchResponse = elasticsearchHelper.getRestHighLevelClient().search(searchRequest, RequestOptions.DEFAULT);
            final SearchHit[] hits = searchResponse.getHits().getHits();
            final Map<String, Map<String, Object>> hitMap = new HashMap<>();
            for (final SearchHit hit : hits) {
                final Map<String, Object> childSource = hit.getSourceAsMap();
                if (type == 1) {
                    //分析图需要把不在我们库的数据补全
                    hitMap.put(childSource.get("public_code").toString(), childSource);
                } else {
                    //列表，我们库有几条返回几条
                    if (childSource.get("title_cn") == null) {
                        childSource.put("title_cn", childSource.get("title"));
                    }
                    children.add(childSource);
                }

            }
            if (type == 1) {
                //分析图数据补全
                for (final String item : list) {
                    if (hitMap.get(item) != null) {
                        children.add(hitMap.get(item));
                    } else {
                        //不在我们库，没法给详细信息
                        final Map<String, Object> noDataMap = new HashMap<>();
                        noDataMap.put("public_code", item);
                        children.add(noDataMap);
                    }
                }
            }
        }

        return children;
    }

    @Override
    public Map<String, Object> cognateMap(final String id, final Integer type, final List<String> country) throws IOException {
        final String[] includes = {"id", "simple_family", "simple_family_num", "complete_family", "complete_family_num"};
        final Map<String, Object> source = elasticsearchHelper.getDataById(EsIndexEnum.PATENT.getEsIndex(), id, includes, new String[]{});

        final List<String> simpleFamily = (List<String>) source.get("simple_family");//简单同族
        final List<String> completeFamily = (List<String>) source.get("complete_family");//扩展同族
        if (CollectionUtils.isEmpty(simpleFamily)) {
            source.put("simple_family", null);
            source.put("simple_family_num", 0);
        }
        if (CollectionUtils.isEmpty(completeFamily)) {
            source.put("complete_family", null);
            source.put("complete_family_num", 0);
        }

        //计算国家类别同族专利数
        //数据筛选传参影响整个页面数据，包括同族个数、地图、列表
        //地图和地区传参无关
        final Set<String> family = new HashSet<>();
        if (type == 1) {
            family.addAll(simpleFamily == null ? new ArrayList<>() : simpleFamily);
            family.addAll(completeFamily == null ? new ArrayList<>() : completeFamily);
        } else if (type == 2) {
            family.addAll(simpleFamily == null ? new ArrayList<>() : simpleFamily);
            source.remove("complete_family");
            source.remove("complete_family_num");
        } else if (type == 3) {
            family.addAll(completeFamily == null ? new ArrayList<>() : completeFamily);
            source.remove("simple_family");
            source.remove("simple_family_num");
        }
        final List<PatentPortraitFamilyPatentBO> familyNumForCountry = getFamilyPatentForCountry(new ArrayList<>(family));
        for (final PatentPortraitFamilyPatentBO bo : familyNumForCountry) {
            bo.setNum(bo.getList().size());
        }
        source.put("family_num_for_country", familyNumForCountry);

        return source;
    }

    @Override
    public List<Map<String, Object>> cognateList(final String id, final Integer type, final List<String> country) throws IOException {
        final String[] includes = {"id", "simple_family", "simple_family_num", "complete_family", "complete_family_num"};
        final Map<String, Object> source = elasticsearchHelper.getDataById(EsIndexEnum.PATENT.getEsIndex(), id, includes, new String[]{});

        final List<String> simpleFamily = (List<String>) source.get("simple_family");//简单同族
        final List<String> completeFamily = (List<String>) source.get("complete_family");//扩展同族

        //数据筛选传参影响整个页面数据，包括同族个数、地图、列表
        final Set<String> family = new HashSet<>();
        if (type == 1) {
            family.addAll(simpleFamily == null ? new ArrayList<>() : simpleFamily);
            family.addAll(completeFamily == null ? new ArrayList<>() : completeFamily);
        } else if (type == 2) {
            family.addAll(simpleFamily == null ? new ArrayList<>() : simpleFamily);
        } else if (type == 3) {
            family.addAll(completeFamily == null ? new ArrayList<>() : completeFamily);
        }

        //地区传参只影响列表
        List<String> familyForList = null;
        if (!CollectionUtils.isEmpty(country)) {
            //过滤掉非地区传参的专利
            familyForList = filterPatentByRegion(new ArrayList<>(family), country);
        } else {
            familyForList = new ArrayList<>(family);
        }

        //同族列表
        final List<Map<String, Object>> familyList = getFamilyPatentDetailList(familyForList);
        for (final Map<String, Object> patent : familyList) {
            final String publicCode = patent.get("public_code").toString();
            final List<String> familyType = new ArrayList<>();
            if (simpleFamily != null && simpleFamily.contains(publicCode)) {
                familyType.add("简单");
            }
            if (completeFamily != null && completeFamily.contains(publicCode)) {
                familyType.add("扩展");
            }
            patent.put("family_type", familyType);
        }

        return familyList;
    }

//    private List<String> filterPatentByRegion(List<String> patent, List<PatentPortraitCountryEncodingBO> encodingList) {
//        List<String> result = new ArrayList<>();
//        if (!CollectionUtils.isEmpty(patent)) {
//            if (CollectionUtils.isEmpty(encodingList)) {
//                //有可能库里没有这些地区
//                return new ArrayList<>();
//            } else {
//                for (PatentPortraitCountryEncodingBO bo : encodingList) {
//                    for (String item : patent) {
//                        if (item.substring(0, 2).equals(bo.getEncoding())) {
//                            result.add(item);
//                        }
//                    }
//                }
//            }
//        }
//        return result;
//    }

    private List<String> filterPatentByRegion(final List<String> patent, final List<String> country) throws IOException {
        final List<String> result = new ArrayList<>();
        if (!CollectionUtils.isEmpty(patent)) {
            //查询专利公开国家字段符合传参country的专利
            final SearchRequest searchRequest = new SearchRequest(EsIndexEnum.PATENT.getEsIndex());
            final SearchSourceBuilder searchBuilder = new SearchSourceBuilder();
            searchBuilder.fetchSource(new String[]{"id", "public_code"}, null);
            // 初始化布尔查询
            final BoolQueryBuilder filterBuilder = QueryBuilders.boolQuery();
            filterBuilder.filter(QueryBuilders.termsQuery("public_country", country));
            filterBuilder.filter(QueryBuilders.termsQuery("public_code", patent));
            searchBuilder.query(filterBuilder);
            searchRequest.source(searchBuilder);
            final SearchResponse searchResponse = elasticsearchHelper.getRestHighLevelClient().search(searchRequest, RequestOptions.DEFAULT);
            final SearchHit[] hits = searchResponse.getHits().getHits();
            for (final SearchHit hit : hits) {
                final Map<String, Object> source = hit.getSourceAsMap();
                result.add(source.get("public_code").toString());
            }
        }
        return result;
    }


    private List<Map<String, Object>> getFamilyPatentDetailList(final List<String> patent) throws IOException {
        final List<Map<String, Object>> result = new ArrayList<>();
        if (!CollectionUtils.isEmpty(patent)) {
            final SearchRequest searchRequest = new SearchRequest(EsIndexEnum.PATENT.getEsIndex());
            final SearchSourceBuilder searchBuilder = new SearchSourceBuilder();
            searchBuilder.fetchSource(new String[]{"id", "public_code", "title_cn", "priority_code",
                    "public_date", "apply_code", "apply_date", "public_country", "patent_type", "status"}, null);
            // 初始化布尔查询
            final BoolQueryBuilder filterBuilder = QueryBuilders.boolQuery();
            filterBuilder.filter(QueryBuilders.termsQuery("public_code", patent));
            searchBuilder.size(1000);
            searchBuilder.query(filterBuilder);
            searchRequest.source(searchBuilder);
            final SearchResponse searchResponse = elasticsearchHelper.getRestHighLevelClient().search(searchRequest, RequestOptions.DEFAULT);
            final SearchHit[] hits = searchResponse.getHits().getHits();
            for (final SearchHit hit : hits) {
                final Map<String, Object> source = hit.getSourceAsMap();
                result.add(source);
            }
        }
        return result;
    }

    @Override
    public Map<String, Object> cognateTimeline(final String id, final Integer type) throws IOException {
        final String[] includes = {"id", "public_code", "simple_family", "complete_family"};
        final Map<String, Object> source = elasticsearchHelper.getDataById(EsIndexEnum.PATENT.getEsIndex(), id, includes, new String[]{});

        final List<String> simpleFamily = (List<String>) source.get("simple_family");//简单同族
        final List<String> completeFamily = (List<String>) source.get("complete_family");//扩展同族
        final String publicCode = source.get("public_code").toString();//当前专利
        final Set<String> family = new HashSet<>();
        if (type == 1) {
            family.addAll(simpleFamily == null ? new ArrayList<>() : simpleFamily);
            family.addAll(completeFamily == null ? new ArrayList<>() : completeFamily);
        } else if (type == 2) {
            family.addAll(simpleFamily == null ? new ArrayList<>() : simpleFamily);
        } else if (type == 3) {
            family.addAll(completeFamily == null ? new ArrayList<>() : completeFamily);
        }
        family.add(publicCode);
        final Map<String, Object> familyTimeline = getFamilyTimeline(new ArrayList<>(family), publicCode);

        return familyTimeline;
    }

    private Map<String, Object> getFamilyTimeline(final List<String> patent, final String publicCode) throws IOException {
        final Map<String, Object> result = new HashMap<>();
        final List<PatentPortraitFamilyTimelineBO> dataList = new ArrayList<>();//数据
        final List<String> regionOrdinate = new ArrayList<>();//纵坐标
        if (!CollectionUtils.isEmpty(patent)) {
            final SearchRequest searchRequest = new SearchRequest(EsIndexEnum.PATENT.getEsIndex());
            final SearchSourceBuilder searchBuilder = new SearchSourceBuilder();
            searchBuilder.fetchSource(new String[]{"id", "public_code", "title_cn",
                    "public_date", "public_country"}, null);
            // 初始化布尔查询
            final BoolQueryBuilder filterBuilder = QueryBuilders.boolQuery();
            filterBuilder.filter(QueryBuilders.termsQuery("public_code", patent));
            searchBuilder.query(filterBuilder);
            // 创建日期直方图聚合构建器，按年份聚合
            final DateHistogramAggregationBuilder yearAggregationBuilder = AggregationBuilders
                    .dateHistogram("by_year")
                    .field("public_date") // 替换为你的日期字段名
                    .calendarInterval(DateHistogramInterval.YEAR); // 按年份间隔

            // 创建地区terms聚合构建器
            final TermsAggregationBuilder regionAggregationBuilder = AggregationBuilders
                    .terms("by_region")
                    .field("public_country"); // 替换为你的地区字段名
            // 将地区terms聚合构建器添加到年份直方图聚合构建器
            yearAggregationBuilder.subAggregation(regionAggregationBuilder);
            // 创建top_hits聚合构建器，为每个聚合桶返回相关文档
            final TopHitsAggregationBuilder topHitsAggregationBuilder = AggregationBuilders
                    .topHits("top_hits")
                    .size(100); // 指定每个聚合桶返回的文档数量
            // 将top_hits聚合构建器添加到地区直方图聚合构建器
            regionAggregationBuilder.subAggregation(topHitsAggregationBuilder);
            // 将年份直方图聚合构建器添加到搜索源构建器
            searchBuilder.aggregation(yearAggregationBuilder);
            // 将搜索源构建器设置到搜索请求
            searchRequest.source(searchBuilder);
            final SearchResponse searchResponse = elasticsearchHelper.getRestHighLevelClient().search(searchRequest, RequestOptions.DEFAULT);

            final ParsedDateHistogram yearHistogram = searchResponse.getAggregations().get("by_year");
            // 遍历年份聚合桶
            if (yearHistogram != null) {
                for (final Histogram.Bucket yearBucket : yearHistogram.getBuckets()) {
                    final String year = yearBucket.getKeyAsString().substring(0, 4);
                    final PatentPortraitFamilyTimelineBO timelineBO = new PatentPortraitFamilyTimelineBO();
                    timelineBO.setYear(year);

                    // 获取地区聚合结果
                    final ParsedTerms regionTerms = yearBucket.getAggregations().get("by_region");

                    final List<PatentPortraitFamilyTimelineDataBO> list = new ArrayList<>();
                    // 遍历地区聚合桶
                    if (regionTerms != null) {
                        for (final Terms.Bucket regionBucket : regionTerms.getBuckets()) {
                            final String region = regionBucket.getKeyAsString();
                            final PatentPortraitFamilyTimelineDataBO dataBO = new PatentPortraitFamilyTimelineDataBO();
                            dataBO.setLabel(region);

                            if (!regionOrdinate.contains(region)) {
                                regionOrdinate.add(region);
                            }
                            // 获取每个国家的top_hits聚合
                            final ParsedTopHits topHits = regionBucket.getAggregations().get("top_hits");

                            final List<PatentPortraitFamilyTimelineDetailBO> value = new ArrayList<>();
                            // 遍历top_hits聚合中的文档
                            if (topHits != null) {
                                for (final SearchHit hit : topHits.getHits().getHits()) {
                                    // 获取文档source
                                    final Map<String, Object> source = hit.getSourceAsMap();
                                    final PatentPortraitFamilyTimelineDetailBO detailBO = new PatentPortraitFamilyTimelineDetailBO();
                                    detailBO.setTitle(source.get("title_cn") == null ? "" : source.get("title_cn").toString());
                                    detailBO.setPublicCode(source.get("public_code") == null ? "" : source.get("public_code").toString());
                                    detailBO.setPublicDate(source.get("public_date") == null ? "" : source.get("public_date").toString());
                                    if (source.get("public_code") != null && source.get("public_code").equals(publicCode)) {
                                        detailBO.setIsCurrent(1);
                                    } else {
                                        detailBO.setIsCurrent(0);
                                    }
                                    value.add(detailBO);
                                }
                            }
                            dataBO.setValue(value);
                            list.add(dataBO);
                        }
                    }
                    timelineBO.setList(list);
                    dataList.add(timelineBO);
                }
            }
        }

        result.put("data", dataList);
        result.put("ordinate", regionOrdinate);

        return result;
    }
}
