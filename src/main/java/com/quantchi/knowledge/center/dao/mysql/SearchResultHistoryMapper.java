package com.quantchi.knowledge.center.dao.mysql;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.quantchi.knowledge.center.bean.entity.SearchResultHistory;

/**
 * <p>
 * 搜索结果历史记录表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-18
 */
@DS("sys")
public interface SearchResultHistoryMapper extends BaseMapper<SearchResultHistory> {

}
