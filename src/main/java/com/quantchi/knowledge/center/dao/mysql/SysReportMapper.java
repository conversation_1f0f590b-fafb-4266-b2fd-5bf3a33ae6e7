package com.quantchi.knowledge.center.dao.mysql;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.quantchi.knowledge.center.bean.entity.SysReport;
import com.quantchi.knowledge.center.bean.model.IdNameModel;

import java.util.List;

/**
 * <p>
 * 报告表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-09
 */
@DS("sys")
public interface SysReportMapper extends BaseMapper<SysReport> {

    List<SysReport> getReportList(final String keyword, final List<IdNameModel> xinxingTagList, final List<IdNameModel> weilaiTagList);

}
