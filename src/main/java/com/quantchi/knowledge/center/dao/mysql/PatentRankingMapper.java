package com.quantchi.knowledge.center.dao.mysql;

import com.quantchi.knowledge.center.bean.bo.IndustryRankRdStrengthBO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 专利排名相关的数据库操作
 */
public interface PatentRankingMapper {

    /**
     * 根据节点ID列表和地区名称获取专利排名（包含总量和授权数量）
     *
     * @param nodeIdList 节点ID列表
     * @param province 省份名称
     * @param city 城市名称
     * @param applicantType 申请人类型，“company”表示企业，“university”表示高校
     * @return 专利排名列表（包含总量和授权数量）
     */
    List<IndustryRankRdStrengthBO> getCompanyPatentRankingWithAccreditByNode(
            @Param("nodeIdList") List<String> nodeIdList,
            @Param("province") String province,
            @Param("city") String city,
            @Param("applicantType") String applicantType);

}
