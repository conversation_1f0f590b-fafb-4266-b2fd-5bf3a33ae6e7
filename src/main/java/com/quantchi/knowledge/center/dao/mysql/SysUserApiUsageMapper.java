package com.quantchi.knowledge.center.dao.mysql;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.quantchi.knowledge.center.bean.entity.SysUserApiUsage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * 用户API使用情况Mapper接口
 */
@Mapper
@DS("sys")
public interface SysUserApiUsageMapper extends BaseMapper<SysUserApiUsage> {

    /**
     * 获取用户API使用次数
     * @param userId 用户ID
     * @param apiType API类型
     * @return 使用次数
     */
    @Select("SELECT usage_count FROM sys_user_api_usage WHERE user_id = #{userId} AND api_type = #{apiType}")
    Integer getUserApiUsageCount(@Param("userId") Long userId, @Param("apiType") Integer apiType);
}
