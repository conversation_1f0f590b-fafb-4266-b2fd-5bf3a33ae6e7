package com.quantchi.knowledge.center.dao.mysql;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.quantchi.knowledge.center.bean.bo.CollectionStatusBO;
import com.quantchi.knowledge.center.bean.entity.SysUserCollection;
import com.quantchi.knowledge.center.bean.model.WorkBenchCollectionQuery;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@DS("sys")
public interface SysUserCollectionMapper extends BaseMapper<SysUserCollection> {

    /**
     * 存在则更新，否则插入
     *
     * @param collection
     * @return
     */
    Integer collectExistUpdateOrElseInsert(@Param("collection") SysUserCollection collection);

    /**
     * 取消收藏
     *
     * @param query
     * @return
     */
    int cancelCollect(@Param("query") WorkBenchCollectionQuery query);

    /**
     * 分页获取收藏列表
     *
     * @param userId
     * @param type
     * @param keyword
     * @param index
     * @param pageSize
     * @return
     */
    List<String> getInfoIdListByUserIdAndType(@Param("userId") Long userId,
                                              @Param("type") String type,
                                              @Param("keyword") String keyword,
                                              @Param("index") Integer index,
                                              @Param("pageSize") Integer pageSize);

    /**
     * 获取收藏列表总数
     *
     * @param userId
     * @param type
     * @param keyword
     * @return
     */
    Integer getInfoIdListTotalByUserIdAndType(@Param("userId") Long userId,
                                              @Param("type") String type,
                                              @Param("keyword") String keyword);


    /**
     * 根据用户id、数据idList 查询收藏状态
     *
     * @param userId
     * @param type
     * @param infoIdList
     * @return
     */
    List<CollectionStatusBO> selectCollectStatusById(@Param("userId") Long userId,
                                                     @Param("type") String type,
                                                     @Param("infoIdList") List<String> infoIdList);
}
