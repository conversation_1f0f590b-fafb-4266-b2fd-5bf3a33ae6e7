package com.quantchi.knowledge.center.dao.mysql;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.quantchi.knowledge.center.bean.bo.SysMaterialListBO;
import com.quantchi.knowledge.center.bean.entity.SysUserMaterial;
import com.quantchi.knowledge.center.bean.vo.UserMaterialModuleVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@DS("sys")
public interface SysUserMaterialMapper extends BaseMapper<SysUserMaterial> {

    /**
     * 素材列表
     *
     * @param userId
     * @param keyword
     * @param index
     * @param pageSize
     * @return
     */
    List<SysMaterialListBO> getMaterialList(@Param("userId") Long userId,
                                            @Param("keyword") String keyword,
                                            @Param("index") Integer index,
                                            @Param("pageSize") Integer pageSize);

    /**
     * 素材列表总数
     *
     * @param userId
     * @param keyword
     * @return
     */
    Integer getMaterialListTotal(@Param("userId") Long userId,
                                 @Param("keyword") String keyword);

    /**
     * 按类型查询用户收藏的模块信息
     *
     * @param userId 用户ID
     * @param type   类型
     * @return 模块信息列表
     */
    List<UserMaterialModuleVO.ModuleInfo> getUserMaterialModules(@Param("userId") Long userId,
                                                                 @Param("type") Integer type,
                                                                 @Param("infoId") String infoId);
    
    /**
     * 根据用户ID、类型、信息ID和板块名称查询素材
     *
     * @param userId 用户ID
     * @param type 类型
     * @param infoId 信息ID
     * @param sectorName 板块名称
     * @return 素材信息列表
     */
    List<SysUserMaterial> findByUserIdAndTypeAndInfoIdAndSectorName(
            @Param("userId") Long userId,
            @Param("type") Integer type,
            @Param("infoId") String infoId,
            @Param("sectorName") String sectorName);
}
