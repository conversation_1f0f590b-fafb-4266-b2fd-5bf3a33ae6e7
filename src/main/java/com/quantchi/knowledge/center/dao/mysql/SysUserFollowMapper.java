package com.quantchi.knowledge.center.dao.mysql;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.quantchi.knowledge.center.bean.bo.CommonDataForCountBO;
import com.quantchi.knowledge.center.bean.bo.SysUserFollowBO;
import com.quantchi.knowledge.center.bean.entity.SysUserFollow;
import com.quantchi.knowledge.center.bean.model.WorkBenchFollowQuery;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-27
 */
@DS("sys")
public interface SysUserFollowMapper extends BaseMapper<SysUserFollow> {

    /**
     * 存在则更新，否则插入
     *
     * @param follow
     * @return
     */
    Integer followExistUpdateOrElseInsert(@Param("follow") SysUserFollow follow);

    /**
     * 取消关注
     *
     * @param query
     * @return
     */
    int cancelFollow(@Param("query") WorkBenchFollowQuery query);

    /**
     * 分页获取关注企业id
     *
     * @param userId
     * @param type
     * @param index
     * @param keyword
     * @param pageSize
     * @return
     */
    List<SysUserFollowBO> getInfoIdListByUserIdAndType(@Param("userId") Long userId,
                                                       @Param("type") String type,
                                                       @Param("keyword") String keyword,
                                                       @Param("index") Integer index,
                                                       @Param("pageSize") Integer pageSize);

    /**
     * 获取关注企业总数
     *
     * @param userId
     * @param type
     * @param keyword
     * @return
     */
    Integer getInfoIdListTotalByUserIdAndType(@Param("userId") Long userId,
                                              @Param("type") String type,
                                              @Param("keyword") String keyword);

    /**
     * 根据用户id、数据idList 查询关注状态
     *
     * @param userId
     * @param type
     * @param infoIdList
     * @return
     */
    List<String> selectFollowStatusById(@Param("userId") Long userId,
                                        @Param("type") String type,
                                        @Param("infoIdList") List<String> infoIdList);

    /**
     * 按周聚合近30天走势
     *
     * @param userId
     * @param type
     * @return
     */
    List<CommonDataForCountBO> trendFor30DaysByWeeks(@Param("userId") Long userId,
                                                     @Param("type") String type);


    /**
     * 获取全部关注关联id
     *
     * @param userId
     * @param type
     * @return
     */
    List<String> allInfoIdList(@Param("userId") Long userId,
                               @Param("type") String type);
}
