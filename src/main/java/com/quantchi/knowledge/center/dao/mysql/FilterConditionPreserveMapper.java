package com.quantchi.knowledge.center.dao.mysql;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.quantchi.knowledge.center.bean.entity.FilterConditionPreserve;

/**
 * <p>
 * 筛选记录保存表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-21
 */
@DS("sys")
public interface FilterConditionPreserveMapper extends BaseMapper<FilterConditionPreserve> {

}
