package com.quantchi.knowledge.center.util;

import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.quantchi.knowledge.center.bean.entity.SysFile;
import com.quantchi.knowledge.center.bean.enums.ReportThemeEnum;
import com.quantchi.knowledge.center.bean.exception.BusinessException;
import com.quantchi.knowledge.center.bean.system.vo.AiReportStreamResponse;
import com.quantchi.knowledge.center.config.ModelParamConfig;
import com.quantchi.knowledge.center.service.ISysFileService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Flux;

import java.io.File;
import java.time.Duration;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

/**
 * 外部报告服务API调用客户端
 * 
 * <AUTHOR>
 * @since 2024-12-27
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class ExternalReportApiClient {

    private final ModelParamConfig modelParamConfig;
    private final ISysFileService sysFileService;
    private final WebClient.Builder webClientBuilder;

    /**
     * 调用外部大纲生成API
     *
     * @param title 报告标题
     * @param description 报告描述
     * @param theme 报告主题
     * @param templateFileId 模板文件ID（可选）
     * @return 生成的大纲内容
     */
    public String callOutlineApi(final String title, final String description,
                                final String theme, final Long templateFileId) {
        try {
            // 构建请求参数
            final String url = modelParamConfig.getExternalReportBaseUrl() + "/api/v1/reports/outline";

            // 创建multipart请求
            final HttpRequest request = HttpRequest.post(url)
                    .form("title", title)
                    .form("comment", description)
                    .form("type", convertThemeToType(theme))
                    .timeout(5 * 60 * 1000);

            // 如果有模板文件，添加文件参数
            if (templateFileId != null) {
                try {
                    final File templateFile = getTemplateFileById(templateFileId);
                    request.form("file", templateFile);
                } catch (final Exception e) {
                    log.warn("获取模板文件失败，继续生成大纲", e);
                }
            }

            // 发送请求
            final HttpResponse response = request.execute();

            if (response.getStatus() != 200) {
                throw new BusinessException("外部大纲API调用失败：" + response.body());
            }

            final JSONObject responseBody = JSON.parseObject(response.body());
            if (responseBody.getInteger("code") != 200) {
                throw new BusinessException("外部大纲API返回错误：" + responseBody.getString("message"));
            }

            return responseBody.getString("outline");

        } catch (final Exception e) {
            log.error("调用外部大纲API失败", e);
            throw new BusinessException("大纲生成服务调用失败：" + e.getMessage());
        }
    }

    /**
     * 调用素材检索API
     *
     * @param userId 用户ID
     * @param userRole 用户角色
     * @param query 查询关键词
     * @return 素材检索结果
     */
    public JSONObject callMaterialSearchApi(final String userId, final String userRole, final String query) {
        try {
            // 构建请求参数
            final String url = modelParamConfig.getExternalReportBaseUrl() + "/api/v1/chunk/search";

            // 构建请求体
            final JSONObject requestBody = new JSONObject();
            requestBody.put("user_id", userId);
            requestBody.put("user_role", userRole);
            requestBody.put("query", query);

            // 发送请求
            final HttpResponse response = HttpRequest.post(url)
                    .header("Content-Type", "application/json")
                    .body(requestBody.toJSONString())
                    .timeout(60000)
                    .execute();

            if (response.getStatus() != 200) {
                throw new BusinessException("素材检索API调用失败：" + response.body());
            }

            final JSONObject responseBody = JSON.parseObject(response.body());
            if (responseBody.getInteger("code") != 200) {
                throw new BusinessException("素材检索API返回错误：" + responseBody.getString("message"));
            }

            return responseBody;

        } catch (final Exception e) {
            log.error("调用素材检索API失败", e);
            throw new BusinessException("素材检索服务调用失败：" + e.getMessage());
        }
    }

    /**
     * 流式调用新的AI报告生成API (generate2接口)
     *
     * @param title 报告标题
     * @param outlineContent 大纲内容
     * @param theme 报告主题
     * @return 流式响应
     */
    public Flux<AiReportStreamResponse> callStreamReportApiV2(final String title, final String outlineContent,
                                                             final String theme) {
        try {
            // 构建请求参数
            final String url = modelParamConfig.getExternalReportBaseUrl() + "/api/v1/report/generate2";

            // 构建请求体
            final JSONObject requestBody = new JSONObject();
            requestBody.put("outline", outlineContent);
            requestBody.put("type", convertThemeToType(theme));
            requestBody.put("title", title);

            final WebClient client = webClientBuilder.build();

            log.info("发送流式API请求到新接口: {}", url);
            log.debug("请求体: {}", requestBody.toJSONString());

            return client.post()
                    .uri(url)
                    .header(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)
                    .header(HttpHeaders.ACCEPT, MediaType.TEXT_EVENT_STREAM_VALUE)
                    .bodyValue(requestBody.toJSONString())
                    .retrieve()
                    .bodyToFlux(String.class)
                    .timeout(Duration.ofMinutes(10))
                    .doOnNext(rawData -> log.debug("收到原始数据: {}", rawData))
                    .concatMap(this::parseStreamResponseV2)  // 使用新的解析方法
                    .doOnNext(response -> {
                        log.debug("解析后的响应: event={}, answer={}",
                                response.getEvent(),
                                response.getAnswer() != null ? response.getAnswer().substring(0, Math.min(50, response.getAnswer().length())) + "..." : "null");
                    })
                    .doOnError(e -> log.error("流式API调用失败", e))
                    .onErrorResume(e -> {
                        log.error("流式API调用出错，返回错误响应", e);
                        return Flux.just(createErrorResponse(e.getMessage()));
                    });

        } catch (final Exception e) {
            log.error("构建流式API请求失败", e);
            return Flux.just(createErrorResponse("构建请求失败：" + e.getMessage()));
        }
    }

    /**
     * 流式调用AI报告生成API (原有接口，保留)
     *
     * @param title 报告标题
     * @param description 报告描述
     * @param theme 报告主题
     * @param outlineContent 大纲内容
     * @param templateFileId 模板文件ID（可选）
     * @return 流式响应
     */
    public Flux<AiReportStreamResponse> callStreamReportApi(final String title, final String description,
                                                           final String theme, final String outlineContent,
                                                           final Long templateFileId) {
        try {
            // 构建请求参数
            final String url = "https://tianying-dev.supxmind.quant-chi.com/v1/chat-messages";

            // 构建请求体
            final JSONObject requestBody = new JSONObject();

            // 构建inputs对象，包含outline、title、type三个字段
            final JSONObject inputs = new JSONObject();
            inputs.put("outline", outlineContent);
            inputs.put("title", title);
            inputs.put("type", ReportThemeEnum.getApiTypeByCode(theme));

            requestBody.put("query", "请按照传入的信息生成完整的报告内容，要求内容详实、逻辑清晰、专业性强。");
            requestBody.put("response_mode", "streaming");
            requestBody.put("user", "ai_report_generator");
            requestBody.put("inputs", inputs);

            final WebClient client = webClientBuilder.build();

            log.info("发送流式API请求到: {}", url);
            log.debug("请求体: {}", requestBody.toJSONString());

            return client.post()
                    .uri(url)
                    .header(HttpHeaders.AUTHORIZATION, "Bearer app-7j4U0iKHU5A0SRPQkR5QdqF4")
                    .header(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)
                    .header(HttpHeaders.ACCEPT, MediaType.TEXT_EVENT_STREAM_VALUE)
                    .bodyValue(requestBody.toJSONString())
                    .retrieve()
                    .bodyToFlux(String.class)
                    .timeout(Duration.ofMinutes(10))
                    .doOnNext(rawData -> log.debug("收到原始数据: {}", rawData))
                    .concatMap(this::parseStreamResponse)  // 使用 concatMap 保持顺序并立即处理
                    .doOnNext(response -> {
                        log.info("解析后的响应: event={}, answer={}",
                                response.getEvent(),
                                response.getAnswer() != null ? response.getAnswer().substring(0, Math.min(50, response.getAnswer().length())) + "..." : "null");
                    })
                    .doOnError(e -> log.error("流式API调用失败", e))
                    .onErrorResume(e -> {
                        log.error("流式API调用出错，返回错误响应", e);
                        return Flux.just(createErrorResponse(e.getMessage()));
                    });

        } catch (final Exception e) {
            log.error("构建流式API请求失败", e);
            return Flux.just(createErrorResponse("构建请求失败：" + e.getMessage()));
        }
    }

    /**
     * 解析流式响应数据
     */
    private Flux<AiReportStreamResponse> parseStreamResponse(final String rawData) {
        if (StringUtils.isBlank(rawData)) {
            return Flux.empty();
        }

        try {
            // 直接尝试解析JSON（外部API返回的是纯JSON，不是SSE格式）
            if (rawData.trim().startsWith("{") && rawData.trim().endsWith("}")) {
                return parseJsonResponse(rawData.trim());
            }

            // 处理SSE格式的数据（备用逻辑）
            final String[] lines = rawData.split("\n");
            final List<AiReportStreamResponse> responses = new ArrayList<>();

            for (final String line : lines) {
                if (line.startsWith("data: ")) {
                    final String jsonData = line.substring(6).trim();
                    if (StringUtils.isBlank(jsonData) || "[DONE]".equals(jsonData)) {
                        continue;
                    }
                    responses.addAll(parseJsonResponse(jsonData).collectList().block());
                } else if (line.trim().startsWith("{") && line.trim().endsWith("}")) {
                    // 直接是JSON行
                    responses.addAll(parseJsonResponse(line.trim()).collectList().block());
                }
            }

            return Flux.fromIterable(responses);

        } catch (final Exception e) {
            log.error("解析流式响应失败: {}", rawData, e);
            return Flux.empty();
        }
    }

    /**
     * 解析JSON响应数据
     */
    private Flux<AiReportStreamResponse> parseJsonResponse(final String jsonData) {
        try {
            final JSONObject jsonObject = JSON.parseObject(jsonData);
            final String event = jsonObject.getString("event");

            log.debug("解析JSON事件: event={}", event);

            if ("message".equals(event)) {
                // 处理消息事件
                final String taskId = jsonObject.getString("task_id");
                final String messageId = jsonObject.getString("message_id");
                final String conversationId = jsonObject.getString("conversation_id");
                final String answer = jsonObject.getString("answer");

                final AiReportStreamResponse response = AiReportStreamResponse.createMessageEvent(taskId, messageId, conversationId, answer);
                log.debug("创建消息事件: taskId={}, answer长度={}", taskId, answer != null ? answer.length() : 0);
                return Flux.just(response);

            } else if ("message_end".equals(event)) {
                // 处理结束事件
                final String taskId = jsonObject.getString("task_id");
                final String messageId = jsonObject.getString("message_id");
                final String conversationId = jsonObject.getString("conversation_id");

                final AiReportStreamResponse response = AiReportStreamResponse.createEndEvent(taskId, messageId, conversationId, null, null);
                log.debug("创建结束事件: taskId={}", taskId);
                return Flux.just(response);

            } else {
                // 其他事件类型，记录但不处理
                log.debug("忽略事件类型: {}", event);
                return Flux.empty();
            }

        } catch (final Exception parseException) {
            log.warn("解析JSON失败，跳过: {}", jsonData, parseException);
            return Flux.empty();
        }
    }

    /**
     * 创建错误响应
     */
    private AiReportStreamResponse createErrorResponse(final String errorMessage) {
        final AiReportStreamResponse response = new AiReportStreamResponse();
        response.setEvent("error");
        response.setTaskId(UUID.randomUUID().toString());
        response.setAnswer("生成报告时发生错误：" + errorMessage);
        response.setIsEnd(true);
        response.setCreatedAt(System.currentTimeMillis());
        return response;
    }

    /**
     * 将主题转换为外部API需要的type参数
     */
    private String convertThemeToType(final String theme) {
        switch (theme) {
            case "产业类":
                return "industry";
            case "技术类":
                return "technology";
            case "企业类":
                return "enterprise";
            case "招商类":
                return "investment";
            default:
                return "industry";
        }
    }

    /**
     * 解析新接口的流式响应数据
     */
    private Flux<AiReportStreamResponse> parseStreamResponseV2(final String rawData) {
        if (StringUtils.isBlank(rawData)) {
            return Flux.empty();
        }

        try {
            // 新接口返回的流式数据格式: {"content": "完整产业链"}
            if (rawData.trim().startsWith("{") && rawData.trim().endsWith("}")) {
                final JSONObject jsonObject = JSON.parseObject(rawData.trim());
                final String content = jsonObject.getString("content");

                if (StringUtils.isNotBlank(content)) {
                    // 创建消息事件响应
                    final AiReportStreamResponse response = new AiReportStreamResponse();
                    response.setEvent("message");
                    response.setAnswer(content);
                    response.setIsEnd(false);
                    response.setCreatedAt(System.currentTimeMillis());

                    log.debug("解析新接口响应: content长度={}", content.length());
                    return Flux.just(response);
                }
            }

            return Flux.empty();

        } catch (final Exception e) {
            log.error("解析新接口流式响应失败: {}", rawData, e);
            return Flux.empty();
        }
    }

    /**
     * 通过文件ID获取模板文件
     */
    private File getTemplateFileById(final Long templateFileId) {
        try {
            final SysFile templateFile = sysFileService.getById(templateFileId);
            if (templateFile == null) {
                log.warn("模板文件不存在，fileId: {}", templateFileId);
                return null;
            }

            // 通过文件服务获取文件内容
            final File file = sysFileService.getFileById(templateFileId);
            if (file == null || !file.exists()) {
                log.warn("模板文件内容不存在，fileId: {}", templateFileId);
                return null;
            }

            return file;

        } catch (final Exception e) {
            log.error("获取模板文件失败，fileId: {}", templateFileId, e);
            return null;
        }
    }
}
