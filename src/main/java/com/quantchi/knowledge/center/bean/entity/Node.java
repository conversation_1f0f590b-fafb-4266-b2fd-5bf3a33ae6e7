package com.quantchi.knowledge.center.bean.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-01
 */
@Getter
@Setter
@Accessors(chain = true)
@ApiModel(value = "Node对象", description = "")
@TableName("node")
public class Node implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("结点id，如industry_node_ai/100001")
    private String id;

    @ApiModelProperty("结点名称")
    private String name;

    @ApiModelProperty("层级")
    private Integer level;

    @ApiModelProperty(value = "节点顺序（从0开始）")
    private Integer sequence;

    @ApiModelProperty("结点英文名称")
    private String nameEn;

    @ApiModelProperty("母节点")
    private String parentId;

    @ApiModelProperty("结点描述")
    private String description;

    @ApiModelProperty("创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty("修改时间")
    private LocalDateTime updateTime;

    @ApiModelProperty("是否有效，1有效，0无效")
    private Byte isValid;

    @ApiModelProperty("ICD映射节点id")
    private String icd;

    // 挂接字段，业务中不需要
//    @ApiModelProperty("中文语义词")
//    private String keywordZh;
//
//    @ApiModelProperty("中文排除词")
//    private String excludeKeywordZh;
//
//    @ApiModelProperty("英文语义词")
//    private String keywordEn;
//
//    @ApiModelProperty("英文排除词")
//    private String excludeKeywordEn;
//
//    @ApiModelProperty("关联IPC分类编码")
//    private String ipc;

    public static final String ID = "id";

    public static final String NAME = "name";

    public static final String LEVEL = "level";

    public static final String NAME_EN = "name_en";

    public static final String PARENT_ID = "parent_id";

    public static final String DESC = "desc";

    public static final String ALTER_NAME = "alter_name";

    public static final String CREATE_TIME = "create_time";

    public static final String UPDATE_TIME = "update_time";

    public static final String IS_VALID = "is_valid";

    public static final String CHECKED = "checked";

    public static final String KEYWORD_ZH = "keyword_zh";

    public static final String EXCLUDE_KEYWORD_ZH = "exclude_keyword_zh";

    public static final String KEYWORD_EN = "keyword_en";

    public static final String EXCLUDE_KEYWORD_EN = "exclude_keyword_en";

    public static final String IPC = "ipc";

    public static final String ICD = "icd";
}
