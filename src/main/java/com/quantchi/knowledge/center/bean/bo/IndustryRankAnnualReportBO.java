package com.quantchi.knowledge.center.bean.bo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class IndustryRankAnnualReportBO {

    @ApiModelProperty(value = "序号")
    private Integer rank;

    @ApiModelProperty(value = "企业id")
    private String cid;

    @ApiModelProperty(value = "企业名称")
    private String companyName;

    @ApiModelProperty(value = "营业收入")
    private String grossIncome;

    @ApiModelProperty(value = "净利润")
    private String netProfit;

    @ApiModelProperty(value = "总资产")
    private String totalAssets;

    @ApiModelProperty(value = "年报日期")
    private String date;

    @ApiModelProperty(value = "省")
    private String province;

    @ApiModelProperty(value = "市")
    private String city;

    @ApiModelProperty(value = "地区")
    private String region;
}
