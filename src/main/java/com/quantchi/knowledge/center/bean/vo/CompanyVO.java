package com.quantchi.knowledge.center.bean.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-08
 */
@Data
@ApiModel(value = "企业基本信息")
public class CompanyVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("企业ID")
    private String id;

    @ApiModelProperty("企业名称")
    private String name;

    @ApiModelProperty("曾用名")
    private String usedName;

    @ApiModelProperty("企业标签列表")
    private Set<String> tagList;

    @ApiModelProperty("企业状态")
    private String status;

    @ApiModelProperty("统一社会信用代码")
    private String creditCode;

    @ApiModelProperty("法人")
    private String legalPerson;

    @ApiModelProperty("核准日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date checkDate;

    @ApiModelProperty("logo处理后OSS链接地址")
    private String logo;

    @ApiModelProperty("原始logo链接")
    private String logoSource;

    @ApiModelProperty("员工数量")
    private String employeeSize;

    @ApiModelProperty("参保人数")
    private Integer insuredNumber;

    @ApiModelProperty("联系电话")
    private List<String> telList;

    @ApiModelProperty("电子邮件")
    private List<String> emailList;

    @ApiModelProperty("所属地区")
    private String belongArea;

    @ApiModelProperty("网址")
    private String website;

    @ApiModelProperty("企业地址")
    private String address;

    @ApiModelProperty("企业建立时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date establishDate;

    @ApiModelProperty("实缴资本描述")
    private String payCapi;

    @ApiModelProperty("注册资本描述")
    private String registCapi;

    @ApiModelProperty("组织机构代码")
    private String orgNo;

    @ApiModelProperty("公司类型")
    private String companyType;

    @ApiModelProperty("企业规模")
    private String companyScale;

    @ApiModelProperty("营业期限")
    private String businessTerm;

    @ApiModelProperty("纳税人识别号")
    private String taxpayerNo;

    @ApiModelProperty("国民经济行业分类")
    private List<String> nationIndustryList;

    @ApiModelProperty("新兴产业分类")
    private List<String> strategyNewIndustryList;

    @ApiModelProperty("未来产业分类")
    private List<String> futureIndustryList;

    @ApiModelProperty("量知产业分类")
    private List<String> icdIndustryList;

    @ApiModelProperty("登记机关")
    private String belongOrg;

    @ApiModelProperty("工商注册号")
    private String registrationId;

    @ApiModelProperty("经营范围")
    private String businessScope;

    @ApiModelProperty("企业简介")
    private String description;

    @ApiModelProperty("股票代码")
    private String stockCode;

    @ApiModelProperty("股票简称")
    private String shortName;

    @ApiModelProperty("投资地区分布")
    private List<NameCountVO> investAreaList;

    @ApiModelProperty("被投资企业成立日期分布")
    private List<NameCountVO> investCompanyDateList;


    //2024-07-15李申超新添加字段
    @ApiModelProperty("股票简称")
    private Map<String, Object> mainProduct;

    @ApiModelProperty("是否为上市企业")
    private Boolean isListed;

    @ApiModelProperty("是否用户关注 1是 0否")
    private Integer followStatus;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty("更新时间")
    private Date updateTime;
}
