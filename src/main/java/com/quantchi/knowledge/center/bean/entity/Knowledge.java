package com.quantchi.knowledge.center.bean.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 * 产业链元数据表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-01
 */
@Getter
@Setter
@Accessors(chain = true)
@ApiModel(value = "产业链元数据", description = "产业链元数据表")
@TableName("industry_chain")
public class Knowledge implements Serializable {

    private static final long serialVersionUID = 1L;

    private String id;

    @ApiModelProperty("名称（知识领域）")
    private String name;

    @ApiModelProperty("英文名称（知识领域英文名）")
    private String nameEn;

    @ApiModelProperty("类型（产业链，创新链）")
    private String knowledgeType;

    @ApiModelProperty("链图简介")
    private String description;

    @ApiModelProperty("标签")
    private String label;

    @ApiModelProperty("是否有效")
    private Integer isValid;

    @ApiModelProperty("报告")
    private String report;

    @ApiModelProperty("排序字段")
    private Long sequence;

    @ApiModelProperty("节点数量")
    @TableField(exist = false)
    private Long nodeCount;

    @ApiModelProperty("企业数量")
    @TableField(exist = false)
    private Long companyCount;

    @ApiModelProperty("关系数量")
    @TableField(exist = false)
    private Long relationCount;

    @ApiModelProperty("是否可以可见")
    @TableField(exist = false)
    private Boolean disable = false;

}
