package com.quantchi.knowledge.center.bean.bo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class IndustryRankOutwardInvestBO {

    @ApiModelProperty(value = "序号")
    private Integer rank;

    @ApiModelProperty(value = "企业id")
    private String cid;

    @ApiModelProperty(value = "企业名称")
    private String companyName;

    @ApiModelProperty(value = "直接投资企业数量")
    private Integer directCount;

    @ApiModelProperty(value = "控股企业数量")
    private Integer holdingCount;

    @ApiModelProperty(value = "省")
    private String province;

    @ApiModelProperty(value = "市")
    private String city;

    @ApiModelProperty(value = "地区")
    private String region;
}
