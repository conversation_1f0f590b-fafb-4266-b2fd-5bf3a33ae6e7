package com.quantchi.knowledge.center.bean.system.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <p>
 * AI报告流式响应类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-30
 */
@Data
@ApiModel(value = "AI报告流式响应类")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class AiReportStreamResponse implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("事件类型：message-内容流，message_end-结束")
    private String event;

    @ApiModelProperty("任务ID")
    private String taskId;

    @ApiModelProperty("消息ID")
    private String messageId;

    @ApiModelProperty("会话ID")
    private String conversationId;

    @ApiModelProperty("流式内容片段")
    private String answer;

    @ApiModelProperty("是否结束")
    private Boolean isEnd;

    @ApiModelProperty("报告ID（仅在最后一个流中返回）")
    private Long reportId;

    @ApiModelProperty("完整内容（仅在最后一个流中返回）")
    private String fullContent;

    @ApiModelProperty("创建时间戳")
    private Long createdAt;

    /**
     * 创建消息事件
     */
    public static AiReportStreamResponse createMessageEvent(final String taskId, final String messageId, 
                                                           final String conversationId, final String answer) {
        final AiReportStreamResponse response = new AiReportStreamResponse();
        response.setEvent("message");
        response.setTaskId(taskId);
        response.setMessageId(messageId);
        response.setConversationId(conversationId);
        response.setAnswer(answer);
        response.setIsEnd(false);
        response.setCreatedAt(System.currentTimeMillis());
        return response;
    }

    /**
     * 创建结束事件
     */
    public static AiReportStreamResponse createEndEvent(final String taskId, final String messageId, 
                                                       final String conversationId, final Long reportId, 
                                                       final String fullContent) {
        final AiReportStreamResponse response = new AiReportStreamResponse();
        response.setEvent("message_end");
        response.setTaskId(taskId);
        response.setMessageId(messageId);
        response.setConversationId(conversationId);
        response.setAnswer("");
        response.setIsEnd(true);
        response.setReportId(reportId);
        response.setFullContent(fullContent);
        response.setCreatedAt(System.currentTimeMillis());
        return response;
    }
}
