package com.quantchi.knowledge.center.bean.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class IndustryRankListQuery {

    @ApiModelProperty(value = "产业链id")
    private String chainId;

    @ApiModelProperty(value = "地区")
    private String regionId;

    @ApiModelProperty(value = "节点id")
    private String nodeId;

    @ApiModelProperty(value = "type 1.研发实力2.投资潜力3.资本青睐4.对外投资5.营收规模6.营收利润7.链主企业")
    private Integer type;

    @ApiModelProperty("申请人类型，填university或者company")
    private String applicantType;

    @ApiModelProperty(value = "页码", example = "1")
    private Integer pageNum = 1;

    @ApiModelProperty(value = "每页大小", example = "10")
    private Integer pageSize = 10;
}
