package com.quantchi.knowledge.center.bean.model;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.util.DigestUtils;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class NodeData implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("节点ID")
    private String id;

    @ApiModelProperty("父节点ID")
    private String parentId = "root";

    @ApiModelProperty("节点中文名称")
    private String name;

    @ApiModelProperty("节点英文名称")
    private String nameEn;

    @ApiModelProperty("节点层级(从0开始)")
    private Integer level;

    @ApiModelProperty("节点顺序(从0开始)")
    private Integer sequence;

    @ApiModelProperty("节点类型 0正常 1核心")
    private Integer nodeType;

    @ApiModelProperty("是否高亮显示（当前企业的节点）")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Boolean highlighted;

    @ApiModelProperty("中文语义词")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String keywordZh;

    @ApiModelProperty("中文排除词")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String excludeKeywordZh;

    @ApiModelProperty("英文语义词")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String keywordEn;

    @ApiModelProperty("英文排除词")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String excludeKeywordEn;

    @ApiModelProperty("关联IPC分类编码")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String ipc;

    @ApiModelProperty("子节点")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private List<NodeData> children;

    public static int countNode(final NodeData root) {
        if (root == null) {
            return 0;
        } else {
            int result = 1;
            if (CollectionUtils.isNotEmpty(root.getChildren())) {
                for (final NodeData child : root.getChildren()) {
                    result += countNode(child);
                }
            }
            return result;
        }
    }

    public static int maxDepth(final NodeData root) {
        if (root == null) {
            return 0;
        } else {
            int result = 1;
            if (CollectionUtils.isNotEmpty(root.getChildren())) {
                int tmp = Integer.MIN_VALUE;
                for (final NodeData child : root.getChildren()) {
                    tmp = Math.max(tmp, maxDepth(child));
                }
                result += tmp;
            }
            return result;
        }
    }

    public static String generateNodeId(final String knowledgeId, final String name) {
        return DigestUtils.md5DigestAsHex((knowledgeId + name).getBytes());
    }

    /**
     * 通过节点ID从树中查找对应的节点
     *
     * @param rootNode 树的根节点
     * @param targetId 目标节点的ID
     * @return 找到的目标节点，若找不到返回null
     */
    public static NodeData findNodeById(final NodeData rootNode, final String targetId) {
        if (rootNode == null) {
            return null;
        }
        // 如果当前节点就是目标节点，返回当前节点
        if (rootNode.getId().equals(targetId)) {
            return rootNode;
        }

        // 如果有子节点，递归查找
        if (rootNode.getChildren() != null) {
            for (final NodeData child : rootNode.getChildren()) {
                final NodeData result = findNodeById(child, targetId);
                if (result != null) {
                    return result;
                }
            }
        }

        // 如果当前节点不是目标节点且没有找到目标节点，返回null
        return null;
    }

    /**
     * 从目标节点向上查找父节点中level为2的节点
     *
     * @param node     目标节点
     * @param rootNode 树的根节点
     * @return 找到的level为2的节点，若找不到返回null
     */
    public static NodeData findLevel2Parent(final NodeData node, final NodeData rootNode) {
        if (node.getLevel() == 2) {
            return node;
        }
        // 获取当前节点的父节点
        final String parentId = node.getParentId();
        NodeData parentNode = findNodeById(rootNode, parentId);
        while (parentNode != null && parentNode.getLevel() != 2) {
            parentNode = findNodeById(rootNode, parentNode.getParentId());
        }
        return parentNode;
    }

    /**
     * 根据指定的节点ID获取该节点以及其所有子节点的节点ID。
     *
     * @param nodeId 要搜索的节点的ID。
     * @return 包含节点ID及其所有子节点ID的列表。
     */
    public List<String> getAllChildrenIds(final String nodeId) {
        final List<String> result = new ArrayList<>();

        // 空值检查
        if (nodeId == null || this.id == null) {
            return result;
        }

        // 如果当前节点是查找的节点，将自身ID及所有子节点ID加入结果
        if (this.id.equals(nodeId)) {
            result.add(this.id);
            if (children != null) {
                result.addAll(getChildrenIdsRecursive());
            }
            return result;
        }

        // 递归查找所有子节点
        if (children != null) {
            for (final NodeData child : children) {
                final List<String> childResult = child.getAllChildrenIds(nodeId);
                if (!childResult.isEmpty()) {
                    result.addAll(childResult);
                    break;  // 找到后就可以停止继续查找
                }
            }
        }

        return result;
    }

    /**
     * 递归获取当前节点的所有子节点的ID。
     *
     * @return 当前节点的所有子节点的ID列表。
     */
    private List<String> getChildrenIdsRecursive() {
        final List<String> childIds = new ArrayList<>();
        if (children != null) {
            for (final NodeData child : children) {
                if (child.id != null) {  // 添加空值检查
                    childIds.add(child.id);
                    childIds.addAll(child.getChildrenIdsRecursive());
                }
            }
        }
        return childIds;
    }

    /**
     * 递归添加子节点的ID到列表中
     *
     * @param node        当前节点
     * @param childrenIds 存储子节点ID的列表
     */
    private void addChildrenIds(final NodeData node, final List<String> childrenIds) {
        if (node.getChildren() != null) {
            for (final NodeData child : node.getChildren()) {
                childrenIds.add(child.getId());
                addChildrenIds(child, childrenIds);
            }
        }
    }

}
