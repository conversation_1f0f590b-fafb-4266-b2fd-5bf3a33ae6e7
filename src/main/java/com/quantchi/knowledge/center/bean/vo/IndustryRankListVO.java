package com.quantchi.knowledge.center.bean.vo;

import com.quantchi.knowledge.center.bean.bo.*;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class IndustryRankListVO {

    @ApiModelProperty(value = "研发实力")
    private List<IndustryRankRdStrengthBO> rdStrength;

    @ApiModelProperty(value = "投资潜力")
    private List<IndustryRankInvestPotentialBO> investPotentialList;

    @ApiModelProperty(value = "资本青睐")
    private List<IndustryRankCapitalPreferenceBO> capitalPreferenceList;

    @ApiModelProperty(value = "对外投资")
    private List<IndustryRankOutwardInvestBO> outwardInvestList;

    @ApiModelProperty(value = "营收规模")
    private List<IndustryRankAnnualReportBO> revenueScaleList;

    @ApiModelProperty(value = "营收利润")
    private List<IndustryRankAnnualReportBO> revenueProfitList;

    @ApiModelProperty(value = "链主企业")
    private List<IndustryRankAnnualReportBO> assetsTotalList;

    @ApiModelProperty(value = "当前页码")
    private Integer pageNum;

    @ApiModelProperty(value = "每页大小")
    private Integer pageSize;

    @ApiModelProperty(value = "总记录数")
    private Long total;

    @ApiModelProperty(value = "地区级别：1-省份，2-地级市")
    private Integer regionLevel;

    @ApiModelProperty(value = "地区名称")
    private String regionName;
}
