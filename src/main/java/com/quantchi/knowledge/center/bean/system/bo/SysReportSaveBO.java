package com.quantchi.knowledge.center.bean.system.bo;

import com.quantchi.knowledge.center.bean.vo.TocEntryVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <p>
 * 报告编辑保存类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-30
 */
@Data
@ApiModel(value = "报告编辑保存类")
public class SysReportSaveBO {

    @ApiModelProperty(value = "报告id", required = true)
    @NotNull(message = "报告id不能为空")
    private Long reportId;

    @ApiModelProperty("报告标题")
    private String title;

    @ApiModelProperty("保存数据")
    private String preserveData;

    @ApiModelProperty("用户编辑后的大纲")
    private List<TocEntryVO> outline;

}
