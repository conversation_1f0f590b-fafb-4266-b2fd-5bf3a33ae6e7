package com.quantchi.knowledge.center.bean.bo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class IndustryRankCapitalPreferenceBO {

    @ApiModelProperty(value = "序号")
    private Integer rank;

    @ApiModelProperty(value = "企业id")
    private String cid;

    @ApiModelProperty(value = "企业名称")
    private String companyName;

    @ApiModelProperty(value = "总计融资笔数")
    private Integer financingCount;

    @ApiModelProperty(value = "总计融资金额")
    private String financingValueCalTotal;

    @ApiModelProperty(value = "省")
    private String province;

    @ApiModelProperty(value = "市")
    private String city;

    @ApiModelProperty(value = "地区")
    private String region;
}
