package com.quantchi.knowledge.center.controller;

import cn.dev33.satoken.annotation.SaIgnore;
import cn.hutool.core.text.CharSequenceUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.quantchi.knowledge.center.bean.entity.SchemaFec;
import com.quantchi.knowledge.center.bean.entity.SchemaSec;
import com.quantchi.knowledge.center.bean.enums.EsIndexEnum;
import com.quantchi.knowledge.center.bean.model.EsPageResult;
import com.quantchi.knowledge.center.bean.model.MultidimensionalQuery;
import com.quantchi.knowledge.center.bean.vo.DataPointVO;
import com.quantchi.knowledge.center.bean.vo.IndustryChooseVO;
import com.quantchi.knowledge.center.common.ResultConvert;
import com.quantchi.knowledge.center.common.ResultInfo;
import com.quantchi.knowledge.center.config.annotation.CollectVerify;
import com.quantchi.knowledge.center.config.annotation.RateLimit;
import com.quantchi.knowledge.center.dao.mysql.SchemaFecMapper;
import com.quantchi.knowledge.center.dao.mysql.SchemaSecMapper;
import com.quantchi.knowledge.center.service.SysWorkBenchService;
import com.quantchi.knowledge.center.service.impl.LibraryInfoService;
import com.quantchi.knowledge.center.service.impl.SynthesisSearchService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/8/6 下午4:57
 */
@Slf4j
@RestController
@RequestMapping("/home")
@Api(tags = "首页接口")
@RequiredArgsConstructor
public class HomeController {

    private final SchemaSecMapper schemaSecMapper;

    private final SchemaFecMapper schemaFecMapper;

    private final LibraryInfoService libraryInfoService;

    private final SynthesisSearchService synthesisSearchService;

    private final ThreadPoolTaskExecutor docExecutor;

    private final SysWorkBenchService sysWorkBenchService;

    @GetMapping("/industryList")
    @ApiOperation(value = "获取产业领域列表")
    public ResultInfo<List<IndustryChooseVO>> industryList() {
        final List<IndustryChooseVO> resultList = new ArrayList<>(2);
        final IndustryChooseVO sec = new IndustryChooseVO();
        sec.setType("战新产业");
        final List<SchemaSec> schemaSecs = schemaSecMapper.selectList(Wrappers.<SchemaSec>lambdaQuery()
                .eq(SchemaSec::getLevel, 1)
                .orderByAsc(SchemaSec::getId));
        sec.setIndustryList(schemaSecs.stream().map(dwIndustrySec -> {
            final IndustryChooseVO.IndustryChoose industryChoose = new IndustryChooseVO.IndustryChoose();
            final String name = dwIndustrySec.getName();
            industryChoose.setName(dwIndustrySec.getName());
            if (name.equals("生物产业")) {
                industryChoose.setAlias(name);
            } else {
                industryChoose.setAlias(CharSequenceUtil.subBefore(name, "产业", false));
            }
            industryChoose.setId(dwIndustrySec.getId());
            industryChoose.setDesc(dwIndustrySec.getDesc());
            if (dwIndustrySec.getName().equals("新能源汽车")) {
                industryChoose.setDisable(false);
            }
            industryChoose.setType(2);
            return industryChoose;
        }).collect(Collectors.toList()));
        resultList.add(sec);

        final IndustryChooseVO future = new IndustryChooseVO();
        future.setType("未来产业");
        final List<SchemaFec> schemaFecs = schemaFecMapper.selectList(Wrappers.<SchemaFec>lambdaQuery()
                .eq(SchemaFec::getLevel, 1)
                .orderByAsc(SchemaFec::getId));
        future.setIndustryList(schemaFecs.stream().map(schemaFec -> {
            final IndustryChooseVO.IndustryChoose industryChoose = new IndustryChooseVO.IndustryChoose();
            industryChoose.setName(schemaFec.getName());
            industryChoose.setAlias(CharSequenceUtil.subBefore(schemaFec.getName(), "产业", false));
            industryChoose.setId(schemaFec.getId());
            industryChoose.setDesc(schemaFec.getDesc());
            industryChoose.setType(3);
            return industryChoose;
        }).collect(Collectors.toList()));
        resultList.add(future);
        return ResultConvert.success(resultList);
    }

    @PostMapping("/headline")
    @ApiOperation(value = "获取产业领域对应的头条数据")
    @CollectVerify
    public ResultInfo<List<Map<String, Object>>> homeHeadline(
            @RequestBody final List<String> industryList,
            @RequestParam(required = false, defaultValue = "10") final Integer size,
            @RequestParam final String type) {
        final MultidimensionalQuery query = new MultidimensionalQuery();
        query.setType(type);
        query.setPageNum(1);
        query.setPageSize(size);
        final Map<String, List<String>> termQueries = new HashMap<>();
        termQueries.put("chain_node.id", industryList);
        query.setTermQueries(termQueries);
        final EsPageResult libraryList = libraryInfoService
                .getService(EsIndexEnum.getEsIndexByType(type))
                .getLibraryList(query);
        final List<Map<String, Object>> list = libraryList.getList();
        return ResultConvert.success(sysWorkBenchService.sourceForIndustry(list));
    }

    @GetMapping("/dataCount")
    @ApiOperation(value = "产业数据统计")
    public ResultInfo<List<DataPointVO>> dataCount() {
        return ResultConvert.success(synthesisSearchService.dataCount());
    }

    @GetMapping("/hello")
    @ApiOperation(value = "测试接口")
    @RateLimit(name = "测试接口", rate = 2)
    @SaIgnore
    public ResultInfo<Boolean> hello() {
        return ResultConvert.success(true);
    }

}
