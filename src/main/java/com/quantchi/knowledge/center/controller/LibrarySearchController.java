package com.quantchi.knowledge.center.controller;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.tree.Tree;
import cn.hutool.core.util.StrUtil;
import com.quantchi.knowledge.ai.vo.SuggestReturnVO;
import com.quantchi.knowledge.center.bean.enums.BusinessType;
import com.quantchi.knowledge.center.bean.enums.EsIndexEnum;
import com.quantchi.knowledge.center.bean.exception.BusinessException;
import com.quantchi.knowledge.center.bean.model.*;
import com.quantchi.knowledge.center.common.ResultConvert;
import com.quantchi.knowledge.center.common.ResultInfo;
import com.quantchi.knowledge.center.config.annotation.CollectVerify;
import com.quantchi.knowledge.center.config.annotation.Log;
import com.quantchi.knowledge.center.config.annotation.RateLimit;
import com.quantchi.knowledge.center.service.*;
import com.quantchi.knowledge.center.service.impl.LibraryInfoService;
import com.quantchi.knowledge.center.service.index.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.quantchi.knowledge.center.bean.constant.Constants.LIBRARY_INTERFACE_NOTE;

/**
 * <AUTHOR>
 * @date 2022/7/20 17:19
 */
@Api(tags = "库查询相关接口")
@RestController
@RequestMapping("/library")
@Slf4j
@RequiredArgsConstructor
public class LibrarySearchController {

    private final NavigationSettings navigationSettings;

    private final LibraryInfoService libraryInfoService;
    private final IDataExportService dataExportService;
    private final SchemaNecService schemaNecService;
    private final SchemaFecService schemaFecService;
    private final SchemaSecService schemaSecService;
    private final SchemaIcdService dwIndustryIcdService;
    private final NewsService newsService;
    private final ReportService reportService;
    private final PolicyService policyService;
    private final IcdService icdService;
    private final IpcService ipcService;

    @GetMapping("/getNavSetting")
    @ApiOperation(value = "库筛选条目")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "type", required = true, dataType = "String", dataTypeClass = String.class, value = LIBRARY_INTERFACE_NOTE)
    })
    public ResultInfo<List<CustomIndexNavSetting>> getNavSetting(
            @RequestParam(value = "index", required = false) @Deprecated String index,
            @RequestParam(value = "type", required = false) final String type,
            @RequestParam(value = "termQuery", required = false) final String termQuery) {
        if (StrUtil.isNotBlank(type)) {
            index = EsIndexEnum.getEsIndexByType(type);
        }
        return ResultConvert.success(libraryInfoService.getNavSetting(index, termQuery));
    }

    @PostMapping("/filtering")
    @ApiOperation(value = "库列表查询", notes = LIBRARY_INTERFACE_NOTE)
    @CollectVerify
    @RateLimit(name = "库列表查询", rate = 1000)
    @Log(title = "首页-产业智搜", businessType = BusinessType.QUERY)
    public ResultInfo<EsPageResult> queryByTermsAndKey(
            @NonNull @RequestBody final MultidimensionalQuery query) {
        String index = query.getIndex();
        final String type = query.getType();
        if (StrUtil.isNotBlank(type)) {
            index = EsIndexEnum.getEsIndexByType(type);
            query.setIndex(index);
        }
        // 获取对应的服务类
        return ResultConvert.success(libraryInfoService.getService(index).getLibraryList(query));
    }

    @PostMapping("/patent/advancedSearch")
    @ApiOperation("高级搜索（用于专利库）")
    public ResultInfo<EsPageResult> newAdvancedSearch(@RequestBody final PatentAdvancedSearchQuery aQuery) {
        return ResultConvert.success(libraryInfoService.newAdvancedSearch(aQuery));
    }

    @GetMapping("/advancedSearch/param/search")
    @ApiOperation("高级搜索输入参数搜索")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "index", dataType = "String", dataTypeClass = String.class, value = LIBRARY_INTERFACE_NOTE),
            @ApiImplicitParam(name = "keyWord", dataType = "String", dataTypeClass = String.class, value = "用户输入值"),
            @ApiImplicitParam(name = "field", dataType = "String", dataTypeClass = String.class, value = "所在的筛选项的字段名，如province")})
    public ResultInfo<List<String>> advancedParamSearch(@RequestParam(required = false) @Deprecated String index,
                                                        @RequestParam(required = false) final String type,
                                                        @RequestParam final String keyWord,
                                                        @RequestParam final String field) {
        if (StrUtil.isNotBlank(type)) {
            index = EsIndexEnum.getEsIndexByType(type);
        }
        final List<CustomIndexNavSetting> customIndexNavSettings =
                navigationSettings.getIndexNavSettingMap().get(index);
        final List<List<String>> scopeList =
                customIndexNavSettings.stream().filter(customIndexNavSetting -> customIndexNavSetting.getField().equals(field))
                        .map(CustomIndexNavSetting::getScope).collect(Collectors.toList());
        if (CollUtil.isEmpty(scopeList)) {
            return ResultConvert.success();
        }
        final List<String> strings = scopeList.get(0);
        final List<String> collect = strings.stream().filter(string -> string.contains(keyWord)).collect(Collectors.toList());
        return ResultConvert.success(collect);
    }

    @GetMapping("/fullSearch")
    public ResultInfo<EsPageResult> fullSearch(@RequestParam final String keyword) {
        final FullSearchQuery fullSearchQuery = new FullSearchQuery();
        fullSearchQuery.setKeyword(keyword);
        return ResultConvert.success(libraryInfoService.fullSearch(fullSearchQuery));
    }

    @PostMapping("/suggest")
    @ApiOperation(value = "搜索推荐")
    public ResultInfo<List<SuggestReturnVO>> suggest(@RequestBody final SuggestSearchQuery suggestSearchQuery) throws IOException {
        return ResultConvert.success(libraryInfoService.suggestSearchList(suggestSearchQuery));
    }

    @ApiImplicitParams({
            @ApiImplicitParam(name = "keyword", dataType = "String", dataTypeClass = String.class, value = "用户输入值"),
            @ApiImplicitParam(name = "type", dataType = "Integer", dataTypeClass = Integer.class, value = "1国民经济行业/2战新产业/3未来产业/4量知产业")})
    @ApiOperation(value = "获取产业领域树")
    @GetMapping("/industrialFieldTree")
    public ResultInfo<List<Tree<String>>> industrialFieldTree(
            @RequestParam(required = false) String keyword,
            @RequestParam final Integer type) {
        if (keyword == null) {
            keyword = "";
        }
        final List<Tree<String>> treeList;
        switch (type) {
            case 1:
                treeList = schemaNecService.getTree(keyword);
                break;
            case 2:
                treeList = schemaSecService.getTree(keyword);
                break;
            case 3:
                treeList = schemaFecService.getTree(keyword);
                break;
            case 4:
                treeList = dwIndustryIcdService.getTree(keyword);
                break;
            default:
                throw new BusinessException("不支持这种产业类型");
        }
        return ResultConvert.success(treeList.get(0).getChildren());
    }

    @GetMapping("/news/info")
    @ApiOperation(value = "资讯详情查询", notes = "资讯的标签依次取值为情感属性sentiment、咨询主题tags、产业链industry")
    @CollectVerify
    public ResultInfo<Map<String, Object>> newsInfo(@RequestParam final String id) {
        return ResultConvert.success(newsService.newsInfo(id));
    }

    @GetMapping("/report/info")
    @ApiOperation(value = "研报详情查询", notes = "研报的标签依次取值为研报类型type、产业链industry")
    @CollectVerify
    public ResultInfo<Map<String, Object>> reportInfo(@RequestParam final String id) {
        return ResultConvert.success(reportService.reportInfo(id));
    }

    @GetMapping("/policy/info")
    @ApiOperation(value = "政策详情查询", notes = "政策的标签依次取值为政策级别level、产业链industry")
    @CollectVerify
    public ResultInfo<Map<String, Object>> policyInfo(@RequestParam final String id) {
        return ResultConvert.success(policyService.policyInfo(id));
    }

    @GetMapping("/icd/info")
    @ApiOperation("产业详情查询")
    public ResultInfo<Map<String, Object>> icdInfo(@RequestParam final String id,
                                                   @RequestParam(required = false) final String chainId) {
        return ResultConvert.success(icdService.icdInfo(id, chainId));
    }

    @GetMapping("/ipc/info")
    @ApiOperation("技术详情查询")
    public ResultInfo<Map<String, Object>> ipcInfo(@RequestParam final String code) {
        return ResultConvert.success(ipcService.ipcInfo(code));
    }

    @PostMapping("/icd/filtering")
    @ApiOperation(value = "产业节点中的库列表查询")
    @RateLimit(name = "产业节点中的库列表查询", rate = 1000)
    public ResultInfo<EsPageResult> queryByTermsAndKeyForIcd(
            @NonNull @RequestBody final IcdDataQuery query) {
        return ResultConvert.success(libraryInfoService.queryByTermsAndKeyForIcd(query));
    }

    @PostMapping("/icd/filtering/export")
    @ApiOperation("产业节点中的库列表查询导出")
    @RateLimit(name = "产业节点中的库列表查询导出", rate = 1000)
    public void icdDataListExport(@RequestBody final IcdDataQuery query,
                                  final HttpServletResponse response) throws IOException {
        // 检查用户导出次数是否超过限制
        if (!dataExportService.checkExportLimit()) {
            throw new BusinessException("导出次数已达上限，请明天再试");
        }
        
        // 获取数据
        query.setPageNum(1);
        query.setPageSize(5000);
        query.setIsHighlight(false);
        final EsPageResult esPageResult = libraryInfoService.queryByTermsAndKeyForIcd(query);
        libraryInfoService.getService(EsIndexEnum.getEsIndexByType(query.getType())).icdDataExport(esPageResult, response);
    }

    @PostMapping("/ipc/filtering")
    @ApiOperation(value = "ipc技术的库列表查询")
    @RateLimit(name = "ipc技术的库列表查询", rate = 1000)
    public ResultInfo<EsPageResult> queryByTermsAndKeyForIpc(
            @NonNull @RequestBody final IpcDataQuery query) {
        return ResultConvert.success(libraryInfoService.queryByTermsAndKeyForIpc(query));
    }

    @PostMapping("/ipc/filtering/export")
    @ApiOperation("ipc技术的库列表查询导出")
    @RateLimit(name = "ipc技术的库列表查询导出", rate = 1000)
    public void ipcDataListExport(@RequestBody final IpcDataQuery query,
                                  final HttpServletResponse response) throws IOException {
        // 检查用户导出次数是否超过限制
        if (!dataExportService.checkExportLimit()) {
            throw new BusinessException("导出次数已达上限，请明天再试");
        }
        
        // 获取数据
        query.setPageNum(1);
        query.setPageSize(5000);
        query.setIsHighlight(false);
        final EsPageResult esPageResult = libraryInfoService.queryByTermsAndKeyForIpc(query);
        libraryInfoService.getService(EsIndexEnum.getEsIndexByType(query.getType())).ipcDataExport(esPageResult, response);
    }


}
