package com.quantchi.knowledge.center.controller;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.quantchi.knowledge.center.bean.constant.Constants;
import com.quantchi.knowledge.center.bean.entity.*;
import com.quantchi.knowledge.center.bean.enums.BusinessType;
import com.quantchi.knowledge.center.bean.vo.*;
import com.quantchi.knowledge.center.bean.model.NodeData;
import com.quantchi.knowledge.center.common.ResultConvert;
import com.quantchi.knowledge.center.common.ResultInfo;
import com.quantchi.knowledge.center.config.annotation.Log;
import com.quantchi.knowledge.center.config.aop.AssignCompany;
import com.quantchi.knowledge.center.dao.mysql.CompanyInvestMapper;
import com.quantchi.knowledge.center.dao.mysql.DmDivisionMapper;
import com.quantchi.knowledge.center.service.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-08
 */
@Slf4j
@RestController
@RequestMapping("/company/portrait")
@Api(tags = "企业画像接口")
@RequiredArgsConstructor
public class CompanyPortraitController {

    private final ICompanyService companyService;

    private final ICompanyLeaderService companyLeaderService;

    private final ICompanyShareholderService companyShareholderService;

    private final ICompanyBranchService companyBranchService;

    private final ICompanyChangeService companyChangeService;

    private final DmDivisionMapper dmDivisionMapper;

    private final CompanyInvestMapper companyInvestMapper;

    @GetMapping("/menuItems")
    @ApiOperation("获取企业画像菜单项")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "企业ID", required = true, dataType = "String", dataTypeClass = String.class)
    })
    @AssignCompany
    public ResultInfo<List<CompanyMenuItemVO>> getMenuItems(@RequestParam(required = false) final String id) {
        return ResultConvert.success(companyService.getCompanyMenuItems(id));
    }
    
    @GetMapping("/baseInfo")
    @ApiOperation("企业基础工商信息")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "企业id", required = true, dataType = "String", dataTypeClass = String.class)
    })
    @AssignCompany
    @Log(title = "企业画像", businessType = BusinessType.QUERY)
    public ResultInfo<CompanyVO> baseInfo(@RequestParam(required = false) final String id) {
        return ResultConvert.success(companyService.baseInfo(id));
    }

    @GetMapping("/shareholderInfo")
    @ApiOperation("股东信息")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "企业id", required = true, dataType = "String", dataTypeClass = String.class),
            @ApiImplicitParam(name = "pageNum", value = "页码", required = false, dataType = "Integer", dataTypeClass = Integer.class),
            @ApiImplicitParam(name = "pageSize", value = "每页数量", required = false, dataType = "Integer", dataTypeClass = Integer.class)
    })
    @AssignCompany
    public ResultInfo<PageInfo<CompanyShareholderVO>> shareholderInfo(
            @RequestParam(required = false) final String id,
            @RequestParam(required = false, defaultValue = "1") final Integer pageNum,
            @RequestParam(required = false, defaultValue = "10") final Integer pageSize) {
        PageHelper.startPage(pageNum, pageSize);
        final List<CompanyShareholder> list = companyShareholderService.list(Wrappers.<CompanyShareholder>lambdaQuery()
                .eq(CompanyShareholder::getCid, id)
                .eq(CompanyShareholder::getIsValid, 1)
                .orderByDesc(CompanyShareholder::getStockPercent));
        final PageInfo<CompanyShareholder> pageInfo = new PageInfo<>(list);
        final PageInfo<CompanyShareholderVO> resultPageInfo = new PageInfo<>();
        BeanUtils.copyProperties(pageInfo, resultPageInfo);
        final List<CompanyShareholderVO> voList = new ArrayList<>(list.size());
        // 检查企业是否存在
        final Set<String> existCompanyIdList;
        final List<String> companyIdList = list.stream().map(CompanyShareholder::getShareholderId).filter(Objects::nonNull).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(companyIdList)) {
            final List<Company> companies = companyService.listByIds(companyIdList);
            existCompanyIdList = companies.stream().map(Company::getId).collect(Collectors.toSet());
        } else {
            existCompanyIdList = Collections.emptySet();
        }

        list.forEach(item -> {
            final CompanyShareholderVO vo = new CompanyShareholderVO();
            vo.setShareholderName(item.getShareholderName());
            if (existCompanyIdList.contains(item.getShareholderId())) {
                vo.setShareholderId(item.getShareholderId());
            }
            vo.setShareholderType(item.getShareholderType());
            final BigDecimal stockPercent = item.getStockPercent();
            if (stockPercent != null) {
                vo.setStockPercent(stockPercent.setScale(2, RoundingMode.HALF_UP).toPlainString() + "%");
            }
            final BigDecimal subscribedAmount = item.getSubscribedAmount();
            if (subscribedAmount != null) {
                vo.setSubscribedAmount(subscribedAmount.divide(BigDecimal.valueOf(10000), 2, RoundingMode.HALF_UP).toPlainString());
            }
            vo.setSubscribedDate(item.getSubscribedDate());
            final BigDecimal paidAmount = item.getPaidAmount();
            if (paidAmount != null) {
                vo.setPaidAmount(paidAmount.divide(BigDecimal.valueOf(10000), 2, RoundingMode.HALF_UP).toPlainString());
            }
            vo.setPaidDate(item.getPaidDate());
            voList.add(vo);
        });
        resultPageInfo.setList(voList);
        return ResultConvert.success(resultPageInfo);
    }


    @GetMapping("/keyPersonnel")
    @ApiOperation("主要人员")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "企业id", required = true, dataType = "String", dataTypeClass = String.class),
            @ApiImplicitParam(name = "pageNum", value = "页码", required = false, dataType = "Integer", dataTypeClass = Integer.class),
            @ApiImplicitParam(name = "pageSize", value = "每页数量", required = false, dataType = "Integer", dataTypeClass = Integer.class)
    })
    @AssignCompany
    public ResultInfo<PageInfo<CompanyLeaderVO>> keyPersonnel(
            @RequestParam(required = false) final String id,
            @RequestParam(required = false, defaultValue = "1") final Integer pageNum,
            @RequestParam(required = false, defaultValue = "10") final Integer pageSize) {
        PageHelper.startPage(pageNum, pageSize);
        final List<CompanyLeader> companyLeaderList = companyLeaderService.list(Wrappers.<CompanyLeader>lambdaQuery()
                .eq(CompanyLeader::getCid, id)
                .eq(CompanyLeader::getIsValid, 1)
                .orderByAsc(CompanyLeader::getLzLevel)
                .orderByAsc(CompanyLeader::getLevel));
        final PageInfo<CompanyLeader> pageInfo = new PageInfo<>(companyLeaderList);
        final PageInfo<CompanyLeaderVO> resultPageInfo = new PageInfo<>();
        BeanUtils.copyProperties(pageInfo, resultPageInfo);
        final List<CompanyLeaderVO> voList = new ArrayList<>(companyLeaderList.size());
        companyLeaderList.forEach(companyLeader -> {
            final CompanyLeaderVO vo = new CompanyLeaderVO();
            vo.setName(companyLeader.getName());
            vo.setPosition(companyLeader.getPosition());
            vo.setDescription(companyLeader.getDescription());
            voList.add(vo);
        });
        // 根据cid和人员信息去股东信息表获取持股比例
        final List<String> nameList = voList.stream().map(CompanyLeaderVO::getName).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(nameList)) {
            final List<CompanyShareholder> list = companyShareholderService.list(Wrappers.<CompanyShareholder>lambdaQuery()
                    .eq(CompanyShareholder::getCid, id)
                    .eq(CompanyShareholder::getIsValid, 1)
                    .in(CompanyShareholder::getShareholderName, nameList));
            final Map<String, BigDecimal> shareholderPercentMap = list.stream()
                    .filter(item -> item.getStockPercent() != null).collect(Collectors.toMap(CompanyShareholder::getShareholderName, CompanyShareholder::getStockPercent, (a, b) -> a));
            voList.forEach(vo -> {
                final BigDecimal shareholderPercent = shareholderPercentMap.get(vo.getName());
                if (shareholderPercent != null) {
                    vo.setShareholdingRatio(shareholderPercent.setScale(2, RoundingMode.HALF_UP).toPlainString() + "%");
                }
            });
        }
        resultPageInfo.setList(voList);
        return ResultConvert.success(resultPageInfo);
    }

    @GetMapping("/investment")
    @ApiOperation("对外投资企业")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "企业id", required = true, dataType = "String", dataTypeClass = String.class),
            @ApiImplicitParam(name = "pageNum", value = "页码", dataType = "Integer", dataTypeClass = Integer.class),
            @ApiImplicitParam(name = "pageSize", value = "每页数量", dataType = "Integer", dataTypeClass = Integer.class)
    })
    @AssignCompany
    public ResultInfo<PageInfo<CompanyInvestVO>> investment(
            @RequestParam(required = false) final String id,
            @RequestParam(required = false, defaultValue = "1") final Integer pageNum,
            @RequestParam(required = false, defaultValue = "10") final Integer pageSize) {
        //投资事件 不去重
        List<CompanyInvest> list = companyInvestMapper.getDirectInvestByCid(id, null, null, 0);
        final Set<String> provinceCodeList = list.stream().map(CompanyInvest::getInvestProvinceCode).filter(Objects::nonNull).collect(Collectors.toSet());
        final Set<String> cityCodeList = list.stream().map(CompanyInvest::getInvestCityCode).filter(Objects::nonNull).collect(Collectors.toSet());
        final Set<String> areaCodeList = list.stream().map(CompanyInvest::getInvestAreaCode).filter(Objects::nonNull).collect(Collectors.toSet());
        final Collection<String> union = CollUtil.union(provinceCodeList, cityCodeList, areaCodeList);
        final Map<String, String> codeMap;
        if (CollUtil.isNotEmpty(union)) {
            final List<DmDivision> dmDivisions = dmDivisionMapper.selectList(Wrappers.<DmDivision>lambdaQuery()
                    .in(DmDivision::getCode, union));
            codeMap = dmDivisions.stream().collect(Collectors.toMap(DmDivision::getCode, DmDivision::getName));
        } else {
            codeMap = new HashMap<>();
        }
        final PageInfo<CompanyInvestVO> resultPageInfo = new PageInfo<>();
        resultPageInfo.setTotal(list.size());
        list = list.stream()
                .sorted(Comparator.comparing(CompanyInvest::getInvestedEstablishedDate, Comparator.nullsLast(Comparator.naturalOrder()))
                        .reversed())
                .skip((long) (pageNum - 1) * pageSize)
                .limit(pageSize)
                .collect(Collectors.toList());
        final List<CompanyInvestVO> voList = new ArrayList<>(list.size());
        Set<String> existInvestedIdList = list.stream().map(CompanyInvest::getInvestedId).collect(Collectors.toSet());
        if (CollUtil.isNotEmpty(existInvestedIdList)) {
            final List<Company> companyList = companyService.list(Wrappers.<Company>lambdaQuery()
                    .select(Company::getId)
                    .in(Company::getId, existInvestedIdList));
            existInvestedIdList = companyList.stream().map(Company::getId).collect(Collectors.toSet());
        }
        for (final CompanyInvest item : list) {
            final CompanyInvestVO vo = new CompanyInvestVO();
            if (existInvestedIdList.contains(item.getInvestedId())) {
                vo.setInvestedId(item.getInvestedId());
            }
            vo.setInvestedName(item.getInvestedName());
            vo.setInvestedStatus(item.getInvestedStatus());
            vo.setInvestedEstablishedDate(item.getInvestedEstablishedDate());
            final BigDecimal investRatio = item.getInvestRatio();
            if (investRatio != null) {
                // 保留两位小数，然后加上‘%’
                vo.setInvestRatio(investRatio.setScale(2, RoundingMode.HALF_UP).toPlainString() + "%");
            }
            vo.setInvestAmount(item.getInvestAmount());
            // 所属地区，使用省市代码获取省市区名称，然后拼接出来
            vo.setBelongArea(Constants.getBelongArea(codeMap.getOrDefault(item.getInvestProvinceCode(), ""),
                    codeMap.getOrDefault(item.getInvestCityCode(), ""),
                    codeMap.getOrDefault(item.getInvestAreaCode(), "")));
            vo.setInvestedIndustry(item.getInvestedIndustry());
            vo.setInvestType(item.getInvestType());
            voList.add(vo);
        }
        resultPageInfo.setList(voList);
        return ResultConvert.success(resultPageInfo);
    }

    @GetMapping("/branch")
    @ApiOperation("分支机构")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "企业id", required = true, dataType = "String", dataTypeClass = String.class),
            @ApiImplicitParam(name = "pageNum", value = "页码", required = false, dataType = "Integer", dataTypeClass = Integer.class),
            @ApiImplicitParam(name = "pageSize", value = "每页数量", required = false, dataType = "Integer", dataTypeClass = Integer.class)
    })
    @AssignCompany
    public ResultInfo<PageInfo<CompanyBranchVO>> branch(
            @RequestParam(required = false) final String id,
            @RequestParam(required = false, defaultValue = "1") final Integer pageNum,
            @RequestParam(required = false, defaultValue = "10") final Integer pageSize) {
        PageHelper.startPage(pageNum, pageSize);
        final List<CompanyBranch> list = companyBranchService.list(Wrappers.<CompanyBranch>lambdaQuery()
                .eq(CompanyBranch::getIsValid, 1)
                .eq(CompanyBranch::getCid, id));
        final PageInfo<CompanyBranch> pageInfo = new PageInfo<>(list);
        final PageInfo<CompanyBranchVO> resultPageInfo = new PageInfo<>();
        BeanUtils.copyProperties(pageInfo, resultPageInfo);
        final List<CompanyBranchVO> voList = new ArrayList<>(list.size());
        final List<String> branchIdList = new ArrayList<>(list.size());
        list.forEach(item -> {
            final CompanyBranchVO vo = new CompanyBranchVO();
            final String branchId = item.getBranchId();
            vo.setBranchId(branchId);
            vo.setBranchName(item.getBranchName());
            if (branchId != null) {
                branchIdList.add(branchId);
            }
            voList.add(vo);
        });
        // 从企业主表里获取企业信息
        if (!branchIdList.isEmpty()) {
            final List<Company> branchComanyList = companyService.list(Wrappers.<Company>lambdaQuery()
                    .in(Company::getId, branchIdList));
            if (!branchComanyList.isEmpty()) {
                final Map<String, Company> collect = branchComanyList.stream().collect(Collectors.toMap(Company::getId, Function.identity()));
                voList.forEach(vo -> {
                    final Company company = collect.get(vo.getBranchId());
                    if (company != null) {
                        vo.setStatus(company.getStatus());
                        vo.setBelongArea(Constants.getBelongArea(company.getProvince(), company.getCity(), company.getArea()));
                        vo.setEstablishDate(company.getEstablishDate());
                        vo.setDirector(company.getLegalPerson());
                    } else {
                        vo.setBranchId(null);
                    }
                });
            } else {
                voList.forEach(vo -> vo.setBranchId(null));
            }
        }
        resultPageInfo.setList(voList);
        return ResultConvert.success(resultPageInfo);
    }

    @GetMapping("/change")
    @ApiOperation("变更记录")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "企业id", required = true, dataType = "String", dataTypeClass = String.class),
            @ApiImplicitParam(name = "pageNum", value = "页码", required = false, dataType = "Integer", dataTypeClass = Integer.class),
            @ApiImplicitParam(name = "pageSize", value = "每页数量", required = false, dataType = "Integer", dataTypeClass = Integer.class)
    })
    @AssignCompany
    public ResultInfo<PageInfo<CompanyChangeVO>> change(
            @RequestParam(required = false) final String id,
            @RequestParam(required = false, defaultValue = "1") final Integer pageNum,
            @RequestParam(required = false, defaultValue = "10") final Integer pageSize) {
        PageHelper.startPage(pageNum, pageSize);
        final List<CompanyChange> list = companyChangeService.list(Wrappers.<CompanyChange>lambdaQuery()
                .eq(CompanyChange::getCid, id)
                .orderByDesc(CompanyChange::getChangeDate));
        final PageInfo<CompanyChange> pageInfo = new PageInfo<>(list);
        final PageInfo<CompanyChangeVO> resultPageInfo = new PageInfo<>();
        BeanUtils.copyProperties(pageInfo, resultPageInfo);
        final List<CompanyChangeVO> voList = new ArrayList<>(list.size());
        list.forEach(item -> {
            final CompanyChangeVO vo = new CompanyChangeVO();
            vo.setChangeDate(item.getChangeDate());
            vo.setChangeProject(item.getChangeProject());
            vo.setChangeBefore(item.getChangeBefore());
            vo.setChangeAfter(item.getChangeAfter());
            voList.add(vo);
        });
        resultPageInfo.setList(voList);
        return ResultConvert.success(resultPageInfo);
    }

    @GetMapping("/chains")
    @ApiOperation("获取企业对应的所有链信息")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "企业ID", required = true, dataType = "String", dataTypeClass = String.class)
    })
    public ResultInfo<List<CompanyChainVO>> getCompanyChains(@RequestParam(required = false) final String id) {
        return ResultConvert.success(companyService.getCompanyChains(id));
    }

    @GetMapping("/chainTree")
    @ApiOperation("根据链ID获取链图树结构数据，并高亮显示当前企业的节点")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "companyId", value = "企业ID", required = true, dataType = "String", dataTypeClass = String.class),
            @ApiImplicitParam(name = "chainId", value = "链ID", required = true, dataType = "String", dataTypeClass = String.class)
    })
    public ResultInfo<NodeData> getChainTreeData(@RequestParam final String companyId, @RequestParam final String chainId) {
        return ResultConvert.success(companyService.getChainTreeData(companyId, chainId));
    }


}
