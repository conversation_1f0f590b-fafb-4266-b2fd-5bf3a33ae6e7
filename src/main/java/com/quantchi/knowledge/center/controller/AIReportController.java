package com.quantchi.knowledge.center.controller;

import cn.dev33.satoken.annotation.SaCheckLogin;
import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.text.CharSequenceUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.quantchi.knowledge.ai.client.ChatBotClient;
import com.quantchi.knowledge.ai.entity.chatmodel.ChatQueryRequest;
import com.quantchi.knowledge.ai.entity.request.ReportPromptPageRequest;
import com.quantchi.knowledge.ai.entity.response.ChatResponse;
import com.quantchi.knowledge.ai.vo.PromptTemplateConfigVO;
import com.quantchi.knowledge.center.bean.bo.ReportPreserveBO;
import com.quantchi.knowledge.center.bean.dto.*;
import com.quantchi.knowledge.center.bean.entity.PromptTemplateConfig;
import com.quantchi.knowledge.center.bean.entity.ReportPreserve;
import com.quantchi.knowledge.center.bean.entity.ReportToc;
import com.quantchi.knowledge.center.bean.enums.BusinessType;
import com.quantchi.knowledge.center.bean.vo.*;
import com.quantchi.knowledge.center.common.ResultConvert;
import com.quantchi.knowledge.center.common.ResultInfo;
import com.quantchi.knowledge.center.config.ModelParamConfig;
import com.quantchi.knowledge.center.config.annotation.Log;
import com.quantchi.knowledge.center.service.*;
import com.quantchi.knowledge.center.util.HttpCallClient;
import com.quantchi.knowledge.center.util.HttpServletRequestUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpEntity;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.springframework.beans.BeanUtils;
import org.springframework.http.MediaType;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;
import reactor.core.publisher.Flux;

import javax.servlet.http.HttpServletResponse;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.time.Duration;
import java.util.Arrays;
import java.util.List;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/2/24 10:47
 */
@RestController
@RequestMapping("/report")
@Api(tags = "智能报告接口")
@RequiredArgsConstructor
@Slf4j
public class AIReportController {

    private final SysWorkBenchService sysWorkBenchService;

    private final IPromptTemplateConfigService promptTemplateConfigService;

    private final ChatBotClient chatBotClient;

    private final IReportPreserveService reportPreserveService;

    private final IndustryChainReportService industryChainReportService;

    private final IReportTocService reportTocService;

    private final ModelParamConfig modelParamConfig;

    private final ThreadPoolTaskExecutor docExecutor;

    @PostMapping("/pagePrompt")
    @ApiOperation("prompt分页列表")
    public ResultInfo<PageInfo<PromptTemplateConfigVO>> promptList(@RequestBody final ReportPromptPageRequest pageBO) {
        final String category = pageBO.getCategory();
        final Integer subType = pageBO.getSubType();
        final String keyword = pageBO.getKeyword();
        PageHelper.startPage(pageBO.getPageNum(), pageBO.getPageSize());
        final List<PromptTemplateConfig> list = promptTemplateConfigService.list(Wrappers.<PromptTemplateConfig>lambdaQuery()
                .eq(PromptTemplateConfig::getType, 2)
                .eq(CharSequenceUtil.isNotBlank(category), PromptTemplateConfig::getCategory, category)
                .eq(subType != null, PromptTemplateConfig::getSubType, subType)
                .like(CharSequenceUtil.isNotBlank(keyword), PromptTemplateConfig::getContent, keyword));
        final PageInfo<PromptTemplateConfig> configPageInfo = new PageInfo<>(list);
        final List<PromptTemplateConfigVO> collect = list.stream().map(PromptTemplateConfigVO::toVO).collect(Collectors.toList());
        final PageInfo<PromptTemplateConfigVO> resultInfo = new PageInfo<>();
        BeanUtils.copyProperties(configPageInfo, resultInfo);
        resultInfo.setList(collect);
        return ResultConvert.success(resultInfo);
    }

    @GetMapping("/categoryList")
    @ApiOperation("报告灵感种类列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "subType", value = "1提示词", required = true, dataType = "Integer", dataTypeClass = Integer.class)
    })
    public ResultInfo<Set<String>> categoryList(@RequestParam(required = false, defaultValue = "1") final Integer subType) {
        final List<PromptTemplateConfig> list = promptTemplateConfigService.list(
                Wrappers.<PromptTemplateConfig>lambdaQuery()
                        .eq(PromptTemplateConfig::getType, 2)
                        .eq(subType != null, PromptTemplateConfig::getSubType, subType));
        return ResultConvert.success(list.stream().map(PromptTemplateConfig::getCategory).collect(Collectors.toSet()));
    }

    @ApiOperation("智能素材列表")
    @GetMapping("/collectList")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "type", value = "类型 资讯 政策 研报", required = true, dataType = "Integer"),
            @ApiImplicitParam(name = "keyword", value = "关键字", required = true, dataType = "String"),
            @ApiImplicitParam(name = "pageNum", value = "页码", required = true, dataType = "Integer"),
            @ApiImplicitParam(name = "pageSize", value = "每页条数", required = true, dataType = "Integer")
    })
    public ResultInfo<CommonDataForEsVO> collectList(@RequestParam final String type,
                                                     @RequestParam(required = false) final String keyword,
                                                     @RequestParam(required = false, defaultValue = "1") final Integer pageNum,
                                                     @RequestParam(required = false, defaultValue = "6") final Integer pageSize) throws IOException {
        final long userId = StpUtil.getLoginIdAsLong();
        return ResultConvert.success(sysWorkBenchService.collectList(userId, type, keyword, pageNum, pageSize));
    }

    @PostMapping("/save")
    @ApiOperation(value = "报告保存")
    @SaCheckLogin
    @Log(title = "报告中心-报告工厂", businessType = BusinessType.UPDATE)
    public ResultInfo<Boolean> save(@RequestBody final ReportPreserveBO bo) {
        final long userId = StpUtil.getLoginIdAsLong();
        final Long reportId = bo.getReportId();

        // 处理关键词列表，将List转为逗号分隔的字符串
        String keywordListStr = null;
        if (bo.getKeywords() != null && !bo.getKeywords().isEmpty()) {
            keywordListStr = String.join(",", bo.getKeywords());
        }

        boolean result;
        if (reportId != null) {
            final ReportPreserve one = reportPreserveService.getById(reportId);
            // 更新已有记录
            one.setPreserveData(bo.getPreserveData());
            one.setTitle(bo.getTitle());
            one.setKeywordList(keywordListStr);
            one.setSubjectId(bo.getChainId()); // 产业链ID保存到subject_id字段

            result = reportPreserveService.updateById(one);

            // 如果存在目录数据，先删除旧的目录数据
            if (bo.getTocEntries() != null && !bo.getTocEntries().isEmpty()) {
                reportTocService.remove(Wrappers.<ReportToc>lambdaQuery()
                        .eq(ReportToc::getReportId, one.getId()));

                // 保存新的目录数据
                saveTocEntries(bo.getTocEntries(), one.getId());
            }
        } else {
            // 创建新记录
            final ReportPreserve reportPreserve = new ReportPreserve();
            reportPreserve.setPreserveData(bo.getPreserveData());
            reportPreserve.setTitle(bo.getTitle());
            reportPreserve.setKeywordList(keywordListStr);
            reportPreserve.setSubjectId(bo.getChainId()); // 产业链ID保存到subject_id字段
            reportPreserve.setUserId(userId);

            result = reportPreserveService.save(reportPreserve);

            // 保存目录数据
            if (bo.getTocEntries() != null && !bo.getTocEntries().isEmpty()) {
                saveTocEntries(bo.getTocEntries(), reportPreserve.getId());
            }
        }

        return ResultConvert.success(result);
    }

    /**
     * 保存目录条目到report_toc表
     *
     * @param tocEntries 目录条目列表
     * @param reportId 报告ID
     */
    private void saveTocEntries(List<TocEntryVO> tocEntries, Long reportId) {
        if (tocEntries == null || tocEntries.isEmpty() || reportId == null) {
            return;
        }

        List<ReportToc> tocList = tocEntries.stream().map(entry -> {
            ReportToc toc = new ReportToc();
            toc.setTitle(entry.getTitle());
            toc.setLevel(entry.getLevel());
            toc.setAnchorId(entry.getAnchorId());
            toc.setReportId(reportId);
            return toc;
        }).collect(Collectors.toList());

        reportTocService.saveBatch(tocList);
    }

    @GetMapping("/savedReport")
    @ApiOperation(value = "获取保存的报告内容")
    @SaCheckLogin
    public ResultInfo<ReportPreserve> savedReport() {
        final long userId = StpUtil.getLoginIdAsLong();
        return ResultConvert.success(reportPreserveService.getOne(Wrappers.<ReportPreserve>lambdaQuery()
                .eq(ReportPreserve::getUserId, userId)));
    }

    @ApiOperation("报告里的问答")
    @PostMapping(value = "/stream/chats", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public Flux<ChatResponse> chatContStream(@RequestBody final ChatQueryRequest chatQueryRequest) {
        return chatBotClient.chatContOfStream(chatQueryRequest.getMsg(), chatQueryRequest.getMsgUid());
    }

    @ApiOperation("获取产业链报告目录")
    @PostMapping("/industryChainReportToc")
    @SaCheckLogin
    @Log(title = "报告中心-产业链报告目录", businessType = BusinessType.QUERY)
    public ResultInfo<List<TocEntryVO>> getIndustryChainReportToc(
            @RequestBody final IndustryChainReportDTO reportDTO) {
        return ResultConvert.success(industryChainReportService.getIndustryChainReportToc(reportDTO));
    }

    @ApiOperation("获取产业链报告内容")
    @PostMapping("/industryChainReportContent")
    @SaCheckLogin
    public ResultInfo<String> getIndustryChainReportContent(
            @RequestBody final IndustryChainReportContentDTO reportContentDTO) {
        return ResultConvert.success(industryChainReportService.getIndustryChainReportContent(reportContentDTO));
    }

    @ApiOperation("获取产业链报告内容（流式返回）")
    @PostMapping(value = "/industryChainReportContentStream", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    @SaCheckLogin
    public Flux<String> getIndustryChainReportContentStream(
            @RequestBody final IndustryChainReportContentDTO reportContentDTO) {
        // 获取完整的Markdown内容
        String content = industryChainReportService.getIndustryChainReportContent(reportContentDTO);

        // 将内容按行切分成流
        String[] lines = content.split("\\n");
        return Flux.fromArray(lines)
                .delayElements(Duration.ofMillis(10)) // 添加小延迟以模拟流式返回
                .map(line -> line + "\n");
    }

    @ApiOperation("获取报告列表")
    @PostMapping("/list")
    @SaCheckLogin
    @Log(title = "报告中心-报告列表", businessType = BusinessType.QUERY)
    public ResultInfo<List<ReportListVO>> getReportList() {
        final long userId = StpUtil.getLoginIdAsLong();

        // 获取当前用户的所有报告
        List<ReportPreserve> reportList = reportPreserveService.list(Wrappers.<ReportPreserve>lambdaQuery()
                .eq(ReportPreserve::getUserId, userId)
                .orderByDesc(ReportPreserve::getUpdateTime));

        // 转换为VO对象
        List<ReportListVO> resultList = reportList.stream().map(report -> {
            ReportListVO vo = new ReportListVO();
            vo.setId(report.getId());
            vo.setTitle(report.getTitle());
            vo.setChainId(report.getSubjectId());
            vo.setType(report.getType());
            vo.setCreateTime(report.getCreateTime());
            vo.setUpdateTime(report.getUpdateTime());

            // 处理关键词列表，将逗号分隔的字符串转为List
            if (CharSequenceUtil.isNotBlank(report.getKeywordList())) {
                vo.setKeywords(Arrays.asList(report.getKeywordList().split(",")));
            }

            return vo;
        }).collect(Collectors.toList());

        return ResultConvert.success(resultList);
    }

    @ApiOperation("获取报告详情")
    @GetMapping("/detail/{id}")
    @SaCheckLogin
    @Log(title = "报告中心-报告详情", businessType = BusinessType.QUERY)
    public ResultInfo<ReportDetailVO> getReportDetail(@PathVariable("id") Long id) {
        // 获取报告基本信息
        ReportPreserve report = reportPreserveService.getById(id);
        if (report == null) {
            return ResultConvert.error("报告不存在");
        }

        // 获取报告目录信息
        List<ReportToc> tocList = reportTocService.list(Wrappers.<ReportToc>lambdaQuery()
                .eq(ReportToc::getReportId, id)
                .orderByAsc(ReportToc::getId));

        // 转换为VO对象
        ReportDetailVO vo = new ReportDetailVO();
        vo.setId(report.getId());
        vo.setTitle(report.getTitle());
        vo.setChainId(report.getSubjectId());
        vo.setType(report.getType());
        vo.setPreserveData(report.getPreserveData());
        vo.setCreateTime(report.getCreateTime());
        vo.setUpdateTime(report.getUpdateTime());

        // 处理关键词列表
        if (CharSequenceUtil.isNotBlank(report.getKeywordList())) {
            vo.setKeywords(Arrays.asList(report.getKeywordList().split(",")));
        }

        // 处理目录信息
        if (!tocList.isEmpty()) {
            List<TocEntryVO> tocEntries = tocList.stream().map(toc -> {
                TocEntryVO tocEntry = new TocEntryVO();
                tocEntry.setTitle(toc.getTitle());
                tocEntry.setLevel(toc.getLevel().intValue());
                tocEntry.setAnchorId(toc.getAnchorId());
                return tocEntry;
            }).collect(Collectors.toList());

            vo.setTocEntries(tocEntries);
        }

        return ResultConvert.success(vo);
    }
    
    @ApiOperation("删除报告")
    @DeleteMapping("/{id}")
    @SaCheckLogin
    @Log(title = "报告中心-删除报告", businessType = BusinessType.DELETE)
    public ResultInfo<Boolean> deleteReport(@PathVariable("id") Long id) {
        // 获取当前用户ID
        Long userId = StpUtil.getLoginIdAsLong();
        
        // 调用服务删除报告
        boolean result = reportPreserveService.deleteReport(id, userId);
        
        if (result) {
            return ResultConvert.success(true);
        } else {
            return ResultConvert.error("删除失败，请确认报告存在且您有权限删除");
        }
    }
    
    @ApiOperation("重命名报告")
    @PutMapping("/rename/{id}")
    @SaCheckLogin
    @Log(title = "报告中心-重命名报告", businessType = BusinessType.UPDATE)
    public ResultInfo<Boolean> renameReport(
            @PathVariable("id") Long id,
            @RequestParam("newTitle") String newTitle) {
        // 参数校验
        if (CharSequenceUtil.isBlank(newTitle)) {
            return ResultConvert.error("新标题不能为空");
        }
        
        // 获取当前用户ID
        Long userId = StpUtil.getLoginIdAsLong();
        
        try {
            // 调用服务重命名报告
            boolean result = reportPreserveService.renameReport(id, newTitle, userId);
            
            if (result) {
                return ResultConvert.success(true);
            } else {
                return ResultConvert.error("重命名失败，请确认报告存在且您有权限修改");
            }
        } catch (IllegalArgumentException e) {
            // 捕获标题重复异常
            return ResultConvert.error(e.getMessage());
        } catch (Exception e) {
            log.error("重命名报告异常", e);
            return ResultConvert.error("重命名失败：" + e.getMessage());
        }
    }

    @ApiOperation("获取报告关键词")
    @PostMapping("/keywords")
    @SaCheckLogin
    @Log(title = "报告中心-报告关键词", businessType = BusinessType.QUERY)
    public ResultInfo<List<String>> getReportKeywords(@RequestBody final ReportKeywordsDTO keywordsDTO) {
        // 调用外部API获取关键词
        final String apiUrl = modelParamConfig.getDpKeywordUrl();
        final String result = HttpCallClient.postJsonDataToUrl(
                apiUrl,
                modelParamConfig.getDpAccessToken(),
                JSON.toJSONString(keywordsDTO)
        );
        JSONObject jsonResult = JSON.parseObject(result);
        return ResultConvert.success(jsonResult.getJSONArray("keywords").toJavaList(String.class));
    }

    /**
     * 导出报告为Word文档
     *
     * @param id 报告ID
     * @param response HTTP响应对象
     * @throws IOException 如果导出过程中发生IO异常
     */
    @ApiOperation("导出报告为Word文档")
    @GetMapping("/exportWordByDoc4j/{id}")
    public void exportReportToWord(@PathVariable("id") Long id, HttpServletResponse response) throws IOException {
        reportPreserveService.exportReportToWord(id, response);
    }
    
    @ApiOperation("导出报告为Word文档(使用Aspose)")
    @GetMapping("/exportWord/{id}")
    public void exportReportToWordWithAspose(@PathVariable("id") Long id, HttpServletResponse response) throws IOException {
        reportPreserveService.exportReportToWordWithAspose(id, response);
    }

    @ApiOperation("报告润色/扩写/缩写")
    @PostMapping(value = "/stream/write", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public SseEmitter reportPolishStream(@RequestBody final ReportPolishRequest request) {
        // 设置更长的超时时间：30分钟
        final SseEmitter emitter = new SseEmitter(30 * 60 * 1000L);

        // 添加完成、超时和错误处理器
        emitter.onCompletion(() -> log.info("ReportPolish stream completed"));
        emitter.onTimeout(() -> log.info("ReportPolish stream timed out"));
        emitter.onError(e -> log.error("ReportPolish stream error", e));
        final HttpServletResponse httpServletResponse = HttpServletRequestUtil.getResponse();
        // 设置响应
        httpServletResponse.setHeader("Content-Type", "text/event-stream;charset=utf-8");
        httpServletResponse.setHeader("Pragma", "no-cache");
        httpServletResponse.setHeader("Cache-Control", "no-cache");
        httpServletResponse.setHeader("Connection", "keep-alive");
        httpServletResponse.setHeader("Keep-Alive", "timeout=600");
        httpServletResponse.setHeader("Content-Encoding", "none");
        httpServletResponse.setHeader("X-Accel-Buffering", "no");

        // 使用更高优先级的线程执行
        CompletableFuture.runAsync(() -> {
            try {
                // 转换请求为JSON
                String requestJson = JSON.toJSONString(request);
                log.info("Achievement correlation request: {}", requestJson);

                // 创建HTTP客户端并配置
                try (CloseableHttpClient httpClient = HttpClients.createDefault()) {
                    HttpPost httpPost = new HttpPost(modelParamConfig.getDpReportWriteUrl());

                    // 设置请求头和请求体
                    httpPost.setHeader("Content-Type", "application/json");
                    httpPost.setHeader("Accept", "text/event-stream");

                    RequestConfig requestConfig = RequestConfig.custom()
                            .setConnectTimeout(600000)
                            .setSocketTimeout(600000)
                            .build();
                    httpPost.setConfig(requestConfig);
                    httpPost.setEntity(new StringEntity(requestJson, ContentType.APPLICATION_JSON));

                    // 执行请求并获取响应流
                    try (CloseableHttpResponse response = httpClient.execute(httpPost)) {
                        int status = response.getStatusLine().getStatusCode();
                        if (status >= 200 && status < 300) {
                            HttpEntity entity = response.getEntity();
                            if (entity != null) {
                                try (BufferedReader reader = new BufferedReader(
                                        new InputStreamReader(entity.getContent(), StandardCharsets.UTF_8), 8192)) { // 使用更大的缓冲区

                                    String line;
                                    boolean isComplete = false;

                                    // 用于累积SSE事件的数据
                                    StringBuilder eventData = new StringBuilder();
                                    boolean inEvent = false;

                                    while (!isComplete && (line = reader.readLine()) != null) {
                                        // 处理SSE格式的数据
                                        final String currentLine = line; // 为lambda捕获

                                        // 空行表示事件结束
                                        if (currentLine.isEmpty()) {
                                            if (inEvent && eventData.length() > 0) {
                                                // 处理完整的事件数据
                                                String data = eventData.toString().trim();
                                                processEventData(data, emitter);

                                                // 检查是否是结束标记
                                                if (data.contains("[DONE]")) {
                                                    isComplete = true;
                                                    ReportPolishResponse endResponse = new ReportPolishResponse();
                                                    endResponse.setIsEnd(true);
                                                    emitter.send(endResponse);
                                                }

                                                // 重置事件数据
                                                eventData = new StringBuilder();
                                                inEvent = false;
                                            }
                                            continue;
                                        }

                                        // 处理事件行
                                        if (currentLine.startsWith("data:")) {
                                            String data = currentLine.substring(5).trim();
                                            inEvent = true;
                                            eventData.append(data);

                                            // 直接处理[DONE]标记
                                            if ("[DONE]".equals(data)) {
                                                isComplete = true;
                                                ReportPolishResponse endResponse = new ReportPolishResponse();
                                                endResponse.setIsEnd(true);
                                                emitter.send(endResponse);
                                                break;
                                            }

                                            // 尝试立即处理数据（如果是完整的JSON）
                                            try {
                                                ReportPolishResponse correlationResponse = JSON.parseObject(data, ReportPolishResponse.class);
                                                correlationResponse.setIsEnd(false);
                                                emitter.send(correlationResponse);
                                                // 发送后清空缓冲区
                                                eventData = new StringBuilder();
                                                inEvent = false;
                                            } catch (Exception e) {
                                                // JSON不完整，继续累积数据
                                                log.debug("Incomplete JSON data, continuing to accumulate: {}", data);
                                            }
                                        } else if (currentLine.startsWith(":")){  // 注释行
                                            // 忽略注释
                                            continue;
                                        } else if (currentLine.startsWith("event:") || currentLine.startsWith("id:") || currentLine.startsWith("retry:")) {
                                            // 处理其他SSE字段
                                            inEvent = true;
                                            continue;
                                        } else {
                                            // 其他未知行，可能是数据的一部分
                                            if (inEvent) {
                                                eventData.append(currentLine);
                                            }
                                        }
                                    }

                                    // 处理可能剩余的事件数据
                                    if (!isComplete && eventData.length() > 0) {
                                        try {
                                            String data = eventData.toString().trim();
                                            processEventData(data, emitter);
                                        } catch (Exception e) {
                                            log.warn("Failed to process remaining event data", e);
                                        }
                                    }

                                    if (!isComplete) {
                                        // 如果没有收到[DONE]标记，发送结束消息
                                        ReportPolishResponse endResponse = new ReportPolishResponse();
                                        endResponse.setIsEnd(true);
                                        emitter.send(endResponse);
                                    }
                                }
                            }
                        } else {
                            // 处理HTTP错误
                            ReportPolishResponse errorResponse = new ReportPolishResponse();
                            errorResponse.setReasoning_content("HTTP error: " + status);
                            errorResponse.setIsEnd(true);
                            emitter.send(errorResponse);
                        }
                    }
                }
            } catch (Exception e) {
                log.error("Error in achievement correlation stream", e);
                try {
                    ReportPolishResponse errorResponse = new ReportPolishResponse();
                    errorResponse.setReasoning_content("Error processing request: " + e.getMessage());
                    errorResponse.setIsEnd(true);
                    emitter.send(errorResponse);
                    emitter.complete();
                } catch (IOException ex) {
                    log.error("Error sending error response", ex);
                }
            } finally {
                // 确保在所有情况下都完成emitter
                try {
                    emitter.complete();
                } catch (Exception ex) {
                    log.error("Error completing emitter", ex);
                }
            }
        });

        return emitter;
    }

    /**
     * 处理SSE事件数据
     * 尝试解析数据并发送到客户端
     *
     * @param data    事件数据
     * @param emitter SSE发射器
     * @throws IOException 如果发送失败
     */
    private void processEventData(String data, SseEmitter emitter) throws IOException {
        if (data == null || data.isEmpty()) {
            return;
        }

        // 检查是否是结束标记
        if ("[DONE]".equals(data)) {
            ReportPolishResponse endResponse = new ReportPolishResponse();
            endResponse.setIsEnd(true);
            emitter.send(endResponse);
            return;
        }

        try {
            // 尝试解析JSON数据
            ReportPolishResponse correlationResponse = JSON.parseObject(data, ReportPolishResponse.class);
            correlationResponse.setIsEnd(false);
            emitter.send(correlationResponse);
        } catch (Exception e) {
            // 如果不是有效的JSON，尝试发送原始数据
            log.warn("Failed to parse JSON data: {}", data, e);

            // 创建一个简单的响应对象包含原始数据
            ReportPolishResponse fallbackResponse = new ReportPolishResponse();
            fallbackResponse.setReasoning_content(data);
            fallbackResponse.setIsEnd(false);
            emitter.send(fallbackResponse);
        }
    }
}
