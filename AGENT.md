# AGENT.md - ICIR Knowledge Center Project

## Build/Test Commands
- `mvn clean compile` - Compile the project
- `mvn clean package -DskipTests` - Build without tests (default: tests skipped)
- `mvn test` - Run all tests  
- `mvn test -Dtest=SysFileServiceImplTest` - Run single test class
- `mvn test -Dtest=SysFileServiceImplTest#testGetContentTypeByFileName` - Run single test method
- `mvn spring-boot:run` - Start development server (port 8612)

## Architecture & Structure
- **Spring Boot 2.x** app with Java 8, **MyBatis-Plus** ORM, **MySQL** database, **Redis** cache
- **Main class**: `com.quantchi.knowledge.center.CenterApplication`
- **Context path**: `/api`, **Port**: 8612
- **Key modules**: AI chat/report generation, file/material management, search (ElasticSearch), authentication (Sa-Token)
- **External services**: MinIO storage, Aliyun NLP/SMS, DingTalk integration

## Code Style & Conventions
- **Lombok**: Use `@Slf4j` for logging, `@Data` for DTOs, `@RequiredArgsConstructor` for DI
- **Naming**: PascalCase classes with suffixes (VO, BO, Service, Controller), camelCase methods/fields, UPPER_SNAKE_CASE constants
- **Imports**: Organized by package, specific imports preferred, static imports last
- **Spring**: `@RestController` + `@RequestMapping`, `@Service` for business logic, `@Validated` for request validation
- **Error handling**: Custom `BusinessException` with `ErrorCode` enum, centralized `@ControllerAdvice`
- **Tests**: JUnit 5 (`@Test`), reflection for private method testing

## Database & External Dependencies
- **Primary DB**: MySQL with MyBatis-Plus, **Cache**: Redis/Redisson, **Search**: ElasticSearch, **Storage**: MinIO/Aliyun OSS
